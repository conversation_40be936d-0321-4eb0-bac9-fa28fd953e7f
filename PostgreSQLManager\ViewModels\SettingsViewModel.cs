using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using PostgreSQLManager.Models;
using PostgreSQLManager.Security;

namespace PostgreSQLManager.ViewModels
{
    /// <summary>
    /// Settings view model
    /// </summary>
    public partial class SettingsViewModel : ObservableObject
    {
        private readonly ILogger<SettingsViewModel> _logger;
        private readonly IAuthenticationService _authenticationService;
        private readonly ApplicationSettings _settings;

        [ObservableProperty]
        private PostgreSQLSettings _postgreSQLSettings = new();

        [ObservableProperty]
        private ApplicationBehaviorSettings _applicationSettings = new();

        [ObservableProperty]
        private SecuritySettings _securitySettings = new();

        [ObservableProperty]
        private UISettings _userInterfaceSettings = new();

        [ObservableProperty]
        private LoggingSettings _loggingSettings = new();

        [ObservableProperty]
        private string _statusMessage = "Ready";

        [ObservableProperty]
        private bool _hasUnsavedChanges;

        [ObservableProperty]
        private string _newMasterPassword = string.Empty;

        [ObservableProperty]
        private string _confirmMasterPassword = string.Empty;

        [ObservableProperty]
        private string _currentMasterPassword = string.Empty;

        public SettingsViewModel(
            ILogger<SettingsViewModel> logger,
            IAuthenticationService authenticationService,
            ApplicationSettings settings)
        {
            _logger = logger;
            _authenticationService = authenticationService;
            _settings = settings;

            // Create simple copies of settings for editing (simplified to avoid hanging)
            PostgreSQLSettings = new PostgreSQLSettings
            {
                DefaultHost = _settings.PostgreSQL.DefaultHost,
                DefaultPort = _settings.PostgreSQL.DefaultPort,
                DefaultDatabase = _settings.PostgreSQL.DefaultDatabase,
                DefaultUsername = _settings.PostgreSQL.DefaultUsername,
                ServiceName = _settings.PostgreSQL.ServiceName
            };

            ApplicationSettings = new ApplicationBehaviorSettings
            {
                StartWithWindows = _settings.Application.StartWithWindows,
                AutoStartService = _settings.Application.AutoStartService,
                MinimizeToTray = _settings.Application.MinimizeToTray
            };

            SecuritySettings = new SecuritySettings
            {
                EncryptionEnabled = _settings.Security.EncryptionEnabled,
                RequireMasterPassword = _settings.Security.RequireMasterPassword
            };

            UserInterfaceSettings = new UISettings
            {
                Theme = _settings.UI.Theme,
                Language = _settings.UI.Language,
                FontFamily = _settings.UI.FontFamily,
                FontSize = _settings.UI.FontSize
            };

            LoggingSettings = new LoggingSettings
            {
                LogLevel = _settings.Logging.LogLevel,
                EnableFileLogging = _settings.Logging.EnableFileLogging,
                EnableConsoleLogging = _settings.Logging.EnableConsoleLogging
            };
        }

        [RelayCommand]
        private Task SaveSettingsAsync()
        {
            try
            {
                // Copy settings back to the original objects
                CopySettings(PostgreSQLSettings, _settings.PostgreSQL);
                CopySettings(ApplicationSettings, _settings.Application);
                CopySettings(SecuritySettings, _settings.Security);
                CopySettings(UserInterfaceSettings, _settings.UI);
                CopySettings(LoggingSettings, _settings.Logging);

                // Save settings to file (in a real application)
                // await _settingsService.SaveSettingsAsync(_settings);

                HasUnsavedChanges = false;
                StatusMessage = "Settings saved successfully";

                _logger.LogInformation("Application settings saved");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving settings");
                StatusMessage = $"Error saving settings: {ex.Message}";
                return Task.CompletedTask;
            }
        }

        [RelayCommand]
        private void ResetSettings()
        {
            try
            {
                var result = System.Windows.MessageBox.Show(
                    "Are you sure you want to reset all settings to default values? This action cannot be undone.",
                    "Confirm Reset",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Warning);

                if (result != System.Windows.MessageBoxResult.Yes)
                    return;

                // Reset to default values
                PostgreSQLSettings = CloneSettings(new PostgreSQLSettings());
                ApplicationSettings = CloneSettings(new ApplicationBehaviorSettings());
                SecuritySettings = CloneSettings(new SecuritySettings());
                UserInterfaceSettings = CloneSettings(new UISettings());
                LoggingSettings = CloneSettings(new LoggingSettings());

                HasUnsavedChanges = true;
                StatusMessage = "Settings reset to defaults";
                
                _logger.LogInformation("Settings reset to defaults");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting settings");
                StatusMessage = $"Error resetting settings: {ex.Message}";
            }
        }

        [RelayCommand]
        private void DiscardChanges()
        {
            try
            {
                if (!HasUnsavedChanges)
                    return;

                var result = System.Windows.MessageBox.Show(
                    "Are you sure you want to discard all unsaved changes?",
                    "Confirm Discard",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result != System.Windows.MessageBoxResult.Yes)
                    return;

                // Reload original settings
                PostgreSQLSettings = CloneSettings(_settings.PostgreSQL);
                ApplicationSettings = CloneSettings(_settings.Application);
                SecuritySettings = CloneSettings(_settings.Security);
                UserInterfaceSettings = CloneSettings(_settings.UI);
                LoggingSettings = CloneSettings(_settings.Logging);

                HasUnsavedChanges = false;
                StatusMessage = "Changes discarded";
                
                _logger.LogInformation("Settings changes discarded");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error discarding changes");
                StatusMessage = $"Error discarding changes: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task SetMasterPasswordAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(NewMasterPassword))
                {
                    StatusMessage = "Master password cannot be empty";
                    return;
                }

                if (NewMasterPassword != ConfirmMasterPassword)
                {
                    StatusMessage = "Passwords do not match";
                    return;
                }

                if (!await _authenticationService.ValidatePasswordComplexityAsync(NewMasterPassword))
                {
                    StatusMessage = "Password does not meet complexity requirements";
                    return;
                }

                var success = await _authenticationService.SetMasterPasswordAsync(NewMasterPassword);
                
                if (success)
                {
                    StatusMessage = "Master password set successfully";
                    NewMasterPassword = string.Empty;
                    ConfirmMasterPassword = string.Empty;
                    SecuritySettings.RequireMasterPassword = true;
                    HasUnsavedChanges = true;
                }
                else
                {
                    StatusMessage = "Failed to set master password";
                }

                _logger.LogInformation("Master password set: {Success}", success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting master password");
                StatusMessage = $"Error setting master password: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task ChangeMasterPasswordAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(CurrentMasterPassword) || string.IsNullOrEmpty(NewMasterPassword))
                {
                    StatusMessage = "Current and new passwords are required";
                    return;
                }

                if (NewMasterPassword != ConfirmMasterPassword)
                {
                    StatusMessage = "New passwords do not match";
                    return;
                }

                if (!await _authenticationService.ValidatePasswordComplexityAsync(NewMasterPassword))
                {
                    StatusMessage = "New password does not meet complexity requirements";
                    return;
                }

                var success = await _authenticationService.ChangeMasterPasswordAsync(CurrentMasterPassword, NewMasterPassword);
                
                if (success)
                {
                    StatusMessage = "Master password changed successfully";
                    CurrentMasterPassword = string.Empty;
                    NewMasterPassword = string.Empty;
                    ConfirmMasterPassword = string.Empty;
                }
                else
                {
                    StatusMessage = "Failed to change master password";
                }

                _logger.LogInformation("Master password change: {Success}", success);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing master password");
                StatusMessage = $"Error changing master password: {ex.Message}";
            }
        }

        [RelayCommand]
        private Task TestConnectionAsync()
        {
            try
            {
                StatusMessage = "Testing PostgreSQL connection...";

                // Create a test connection using current settings
                var testConnection = new DatabaseConnection
                {
                    Name = "Test Connection",
                    Host = PostgreSQLSettings.DefaultHost,
                    Port = PostgreSQLSettings.DefaultPort,
                    Database = PostgreSQLSettings.DefaultDatabase,
                    Username = PostgreSQLSettings.DefaultUsername
                };

                // This would use the database service to test the connection
                // var success = await _databaseService.TestConnectionAsync(testConnection);
                var success = true; // Placeholder

                StatusMessage = success ? "Connection test successful" : "Connection test failed";

                _logger.LogInformation("PostgreSQL connection test: {Success}", success);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing connection");
                StatusMessage = $"Connection test error: {ex.Message}";
                return Task.CompletedTask;
            }
        }

        [RelayCommand]
        private void BrowseForPath(string pathType)
        {
            try
            {
                // Simplified path browsing without System.Windows.Forms dependency
                // In a real application, you would use a proper folder browser dialog
                StatusMessage = $"Path browsing for {pathType} - Feature available in full version";

                // For demo purposes, set some default paths
                switch (pathType.ToLower())
                {
                    case "installation":
                        PostgreSQLSettings.InstallationPath = @"C:\Program Files\PostgreSQL\16";
                        break;
                    case "data":
                        PostgreSQLSettings.DataDirectory = @"C:\Program Files\PostgreSQL\16\data";
                        break;
                    case "log":
                        PostgreSQLSettings.LogDirectory = @"C:\Program Files\PostgreSQL\16\data\log";
                        break;
                    case "backup":
                        ApplicationSettings.BackupDirectory = @".\Backups";
                        break;
                    case "scripts":
                        ApplicationSettings.ScriptsDirectory = @".\Scripts";
                        break;
                }

                HasUnsavedChanges = true;
                StatusMessage = $"{pathType} path set to default location";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting path");
                StatusMessage = $"Error setting path: {ex.Message}";
            }
        }

        private static T CloneSettings<T>(T original) where T : new()
        {
            // Simplified cloning without reflection to avoid hanging issues
            var clone = new T();

            // Use type-specific copying instead of reflection
            if (original is PostgreSQLSettings pgSettings && clone is PostgreSQLSettings pgClone)
            {
                CopyPostgreSQLSettings(pgSettings, pgClone);
            }
            else if (original is ApplicationBehaviorSettings appSettings && clone is ApplicationBehaviorSettings appClone)
            {
                CopyApplicationSettings(appSettings, appClone);
            }
            else if (original is SecuritySettings secSettings && clone is SecuritySettings secClone)
            {
                CopySecuritySettings(secSettings, secClone);
            }
            else if (original is UISettings uiSettings && clone is UISettings uiClone)
            {
                CopyUISettings(uiSettings, uiClone);
            }
            else if (original is LoggingSettings logSettings && clone is LoggingSettings logClone)
            {
                CopyLoggingSettings(logSettings, logClone);
            }

            return clone;
        }

        private static void CopySettings<T>(T source, T destination)
        {
            // Use type-specific copying instead of reflection
            if (source is PostgreSQLSettings pgSource && destination is PostgreSQLSettings pgDest)
            {
                CopyPostgreSQLSettings(pgSource, pgDest);
            }
            else if (source is ApplicationBehaviorSettings appSource && destination is ApplicationBehaviorSettings appDest)
            {
                CopyApplicationSettings(appSource, appDest);
            }
            else if (source is SecuritySettings secSource && destination is SecuritySettings secDest)
            {
                CopySecuritySettings(secSource, secDest);
            }
            else if (source is UISettings uiSource && destination is UISettings uiDest)
            {
                CopyUISettings(uiSource, uiDest);
            }
            else if (source is LoggingSettings logSource && destination is LoggingSettings logDest)
            {
                CopyLoggingSettings(logSource, logDest);
            }
        }

        public bool HasMasterPassword => SecuritySettings.RequireMasterPassword &&
                                        !string.IsNullOrEmpty(SecuritySettings.MasterPasswordHash);

        // Type-specific copying methods to avoid reflection issues
        private static void CopyPostgreSQLSettings(PostgreSQLSettings source, PostgreSQLSettings destination)
        {
            destination.DefaultHost = source.DefaultHost;
            destination.DefaultPort = source.DefaultPort;
            destination.DefaultDatabase = source.DefaultDatabase;
            destination.DefaultUsername = source.DefaultUsername;
            destination.ConnectionTimeout = source.ConnectionTimeout;
            destination.CommandTimeout = source.CommandTimeout;
            destination.ServiceName = source.ServiceName;
            destination.InstallationPath = source.InstallationPath;
            destination.DataDirectory = source.DataDirectory;
            destination.LogDirectory = source.LogDirectory;
            destination.ConfigFile = source.ConfigFile;
        }

        private static void CopyApplicationSettings(ApplicationBehaviorSettings source, ApplicationBehaviorSettings destination)
        {
            destination.StartWithWindows = source.StartWithWindows;
            destination.AutoStartService = source.AutoStartService;
            destination.MinimizeToTray = source.MinimizeToTray;
            destination.CheckForUpdates = source.CheckForUpdates;
            destination.BackupDirectory = source.BackupDirectory;
            destination.ScriptsDirectory = source.ScriptsDirectory;
            destination.LogsDirectory = source.LogsDirectory;
            destination.ConfigDirectory = source.ConfigDirectory;
            destination.AutoSaveInterval = source.AutoSaveInterval;
            destination.MaxRecentFiles = source.MaxRecentFiles;
            destination.ConfirmDangerousOperations = source.ConfirmDangerousOperations;
        }

        private static void CopySecuritySettings(SecuritySettings source, SecuritySettings destination)
        {
            destination.EncryptionEnabled = source.EncryptionEnabled;
            destination.RequireMasterPassword = source.RequireMasterPassword;
            destination.MasterPasswordHash = source.MasterPasswordHash;
            destination.SessionTimeout = source.SessionTimeout;
            destination.MaxLoginAttempts = source.MaxLoginAttempts;
            destination.LockoutDuration = source.LockoutDuration;
            destination.LogSecurityEvents = source.LogSecurityEvents;
            destination.EncryptScripts = source.EncryptScripts;
            destination.RequirePasswordForScriptExecution = source.RequirePasswordForScriptExecution;
            destination.LastPasswordChange = source.LastPasswordChange;
        }

        private static void CopyUISettings(UISettings source, UISettings destination)
        {
            destination.Theme = source.Theme;
            destination.Language = source.Language;
            destination.FontFamily = source.FontFamily;
            destination.FontSize = source.FontSize;
            destination.ShowLineNumbers = source.ShowLineNumbers;
            destination.WordWrap = source.WordWrap;
            destination.ShowToolbar = source.ShowToolbar;
            destination.ShowStatusBar = source.ShowStatusBar;
            destination.ShowSidebar = source.ShowSidebar;
            destination.WindowWidth = source.WindowWidth;
            destination.WindowHeight = source.WindowHeight;
            destination.WindowLeft = source.WindowLeft;
            destination.WindowTop = source.WindowTop;
        }

        private static void CopyLoggingSettings(LoggingSettings source, LoggingSettings destination)
        {
            destination.LogLevel = source.LogLevel;
            destination.EnableFileLogging = source.EnableFileLogging;
            destination.EnableConsoleLogging = source.EnableConsoleLogging;
            destination.EnableDatabaseLogging = source.EnableDatabaseLogging;
            destination.LogRetentionDays = source.LogRetentionDays;
            destination.MaxLogFileSizeMB = source.MaxLogFileSizeMB;
            destination.LogSqlStatements = source.LogSqlStatements;
            destination.LogPerformanceMetrics = source.LogPerformanceMetrics;
            destination.LogCategories = new List<string>(source.LogCategories);
        }
    }
}
