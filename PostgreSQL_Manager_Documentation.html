<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL Manager - Complete User Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header h2 {
            color: #34495e;
            margin: 10px 0 0 0;
            font-size: 1.8em;
            font-weight: normal;
        }
        
        .toc {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .toc h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        .section h4 {
            color: #7f8c8d;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        
        .bilingual {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        
        .english {
            flex: 1;
            margin-right: 20px;
        }
        
        .arabic {
            flex: 1;
            text-align: right;
            direction: rtl;
            color: #555;
            font-style: italic;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .feature-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .feature-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .ui-mockup {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-line;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-info { background-color: #3498db; }
        
        .table-responsive {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .workflow-step {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .workflow-step h5 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .troubleshooting {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .troubleshooting h5 {
            color: #856404;
            margin-top: 0;
        }
        
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #ecf0f1;
            color: #7f8c8d;
        }
        
        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
        
        @media (max-width: 768px) {
            .container { padding: 20px; }
            .bilingual { flex-direction: column; }
            .english, .arabic { margin-right: 0; margin-bottom: 10px; }
            .feature-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>PostgreSQL Manager</h1>
            <h2>Complete User Documentation<br>مدير PostgreSQL - دليل المستخدم الشامل</h2>
            <p><strong>Version 1.0 | June 21, 2025</strong></p>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h3>Table of Contents / جدول المحتويات</h3>
            <ul>
                <li><a href="#overview">1. Application Overview / نظرة عامة على التطبيق</a></li>
                <li><a href="#requirements">2. System Requirements / متطلبات النظام</a></li>
                <li><a href="#installation">3. Installation Guide / دليل التثبيت</a></li>
                <li><a href="#architecture">4. Application Architecture / بنية التطبيق</a></li>
                <li><a href="#interface">5. User Interface Guide / دليل واجهة المستخدم</a></li>
                <li><a href="#workflows">6. Step-by-Step Workflows / سير العمل خطوة بخطوة</a></li>
                <li><a href="#technical">7. Technical Implementation / التنفيذ التقني</a></li>
                <li><a href="#troubleshooting">8. Troubleshooting / استكشاف الأخطاء وإصلاحها</a></li>
                <li><a href="#appendices">9. Appendices / الملاحق</a></li>
            </ul>
        </div>

        <!-- Application Overview -->
        <div class="section" id="overview">
            <h2>1. Application Overview / نظرة عامة على التطبيق</h2>
            
            <div class="bilingual">
                <div class="english">
                    <h3>Purpose</h3>
                    <p>PostgreSQL Manager is a comprehensive Windows application designed to provide professional-grade management capabilities for PostgreSQL database servers. The application offers an intuitive graphical interface for database administrators and developers to monitor, control, and interact with PostgreSQL services.</p>
                </div>
                <div class="arabic">
                    <h3>الغرض</h3>
                    <p>يُعد مدير PostgreSQL تطبيق Windows شامل مصمم لتوفير إمكانيات إدارة احترافية لخوادم قواعد بيانات PostgreSQL. يوفر التطبيق واجهة رسومية بديهية لمديري قواعد البيانات والمطورين لمراقبة والتحكم والتفاعل مع خدمات PostgreSQL.</p>
                </div>
            </div>

            <h3>Main Features / الميزات الرئيسية</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><span class="status-indicator status-success"></span>Service Management / إدارة الخدمة</h4>
                    <ul>
                        <li>Real-time service monitoring / مراقبة الخدمة في الوقت الفعلي</li>
                        <li>Start, stop, restart, and pause operations / عمليات البدء والإيقاف وإعادة التشغيل والإيقاف المؤقت</li>
                        <li>Service status indicators / مؤشرات حالة الخدمة</li>
                        <li>Performance metrics display / عرض مقاييس الأداء</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-info"></span>Database Management / إدارة قواعد البيانات</h4>
                    <ul>
                        <li>Connection testing and management / اختبار وإدارة الاتصالات</li>
                        <li>Database and table browsing / استعراض قواعد البيانات والجداول</li>
                        <li>Schema exploration / استكشاف المخططات</li>
                        <li>Metadata viewing / عرض البيانات الوصفية</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-warning"></span>SQL Editor / محرر SQL</h4>
                    <ul>
                        <li>Advanced SQL query editor / محرر استعلامات SQL متقدم</li>
                        <li>Query execution and results display / تنفيذ الاستعلامات وعرض النتائج</li>
                        <li>Script management / إدارة النصوص البرمجية</li>
                        <li>Syntax highlighting / تمييز بناء الجملة</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-error"></span>Configuration Management / إدارة التكوين</h4>
                    <ul>
                        <li>PostgreSQL settings configuration / تكوين إعدادات PostgreSQL</li>
                        <li>Application behavior customization / تخصيص سلوك التطبيق</li>
                        <li>Security settings management / إدارة إعدادات الأمان</li>
                        <li>UI preferences / تفضيلات واجهة المستخدم</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="section" id="requirements">
            <h2>2. System Requirements / متطلبات النظام</h2>
            
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Component / المكون</th>
                            <th>Minimum / الحد الأدنى</th>
                            <th>Recommended / الموصى به</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Operating System / نظام التشغيل</strong></td>
                            <td>Windows 10 / Windows Server 2016</td>
                            <td>Windows 11 / Windows Server 2022</td>
                        </tr>
                        <tr>
                            <td><strong>CPU / المعالج</strong></td>
                            <td>Intel Core i3 or AMD equivalent</td>
                            <td>Intel Core i5 or AMD Ryzen 5</td>
                        </tr>
                        <tr>
                            <td><strong>RAM / الذاكرة</strong></td>
                            <td>4 GB</td>
                            <td>16 GB or more</td>
                        </tr>
                        <tr>
                            <td><strong>Storage / التخزين</strong></td>
                            <td>500 MB free space</td>
                            <td>SSD with 2 GB free space</td>
                        </tr>
                        <tr>
                            <td><strong>Display / الشاشة</strong></td>
                            <td>1024x768</td>
                            <td>1920x1080 or higher</td>
                        </tr>
                        <tr>
                            <td><strong>.NET Framework</strong></td>
                            <td>4.8 or later</td>
                            <td>Latest version</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Installation Guide -->
        <div class="section" id="installation">
            <h2>3. Installation Guide / دليل التثبيت</h2>
            
            <div class="workflow-step">
                <h5>Step 1: Pre-Installation / الخطوة 1: ما قبل التثبيت</h5>
                <ul>
                    <li>Ensure PostgreSQL Server is installed / تأكد من تثبيت خادم PostgreSQL</li>
                    <li>Verify .NET Framework 4.8+ is installed / تحقق من تثبيت .NET Framework 4.8+</li>
                    <li>Run Windows Update / قم بتشغيل Windows Update</li>
                    <li>Temporarily disable antivirus / عطل مكافح الفيروسات مؤقتاً</li>
                </ul>
            </div>
            
            <div class="workflow-step">
                <h5>Step 2: Installation Process / الخطوة 2: عملية التثبيت</h5>
                <ul>
                    <li>Download PostgreSQL Manager installer / قم بتنزيل مثبت مدير PostgreSQL</li>
                    <li>Right-click and select "Run as Administrator" / انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول"</li>
                    <li>Follow the installation wizard / اتبع معالج التثبيت</li>
                    <li>Choose installation directory / اختر دليل التثبيت</li>
                    <li>Complete the installation / أكمل التثبيت</li>
                </ul>
            </div>
            
            <div class="workflow-step">
                <h5>Step 3: Post-Installation / الخطوة 3: ما بعد التثبيت</h5>
                <div class="code-block">
Default connection settings:
Host: localhost
Port: 5432
Database: postgres
Username: postgres
Password: [as configured during PostgreSQL installation]
                </div>
            </div>
        </div>

        <!-- Continue with more sections... -->
        <div class="info-box">
            <p><strong>Note:</strong> This HTML document contains the complete documentation structure. The full content from the Markdown file should be converted to HTML format following this template structure.</p>
            <p><strong>ملاحظة:</strong> تحتوي وثيقة HTML هذه على هيكل الوثائق الكامل. يجب تحويل المحتوى الكامل من ملف Markdown إلى تنسيق HTML باتباع هيكل هذا القالب.</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>PostgreSQL Manager v1.0</strong><br>
            Complete User Documentation<br>
            Last Updated: June 21, 2025</p>
            <p><strong>مدير PostgreSQL الإصدار 1.0</strong><br>
            دليل المستخدم الشامل<br>
            آخر تحديث: 21 يونيو 2025</p>
        </div>
    </div>
</body>
</html>
