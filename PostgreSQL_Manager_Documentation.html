<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL Manager - Complete User Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        
        .header h2 {
            color: #34495e;
            margin: 10px 0 0 0;
            font-size: 1.8em;
            font-weight: normal;
        }
        
        .toc {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .toc h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        .section h4 {
            color: #7f8c8d;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        
        .bilingual {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        
        .english {
            flex: 1;
            margin-right: 20px;
        }
        
        .arabic {
            flex: 1;
            text-align: right;
            direction: rtl;
            color: #555;
            font-style: italic;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .feature-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .feature-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .code-block {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .ui-mockup {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre-line;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-info { background-color: #3498db; }
        
        .table-responsive {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .workflow-step {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .workflow-step h5 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .troubleshooting {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .troubleshooting h5 {
            color: #856404;
            margin-top: 0;
        }
        
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #ecf0f1;
            color: #7f8c8d;
        }
        
        @media print {
            body { background-color: white; }
            .container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
        
        @media (max-width: 768px) {
            .container { padding: 20px; }
            .bilingual { flex-direction: column; }
            .english, .arabic { margin-right: 0; margin-bottom: 10px; }
            .feature-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>PostgreSQL Manager</h1>
            <h2>Complete User Documentation<br>مدير PostgreSQL - دليل المستخدم الشامل</h2>
            <p><strong>Version 1.0 | June 21, 2025</strong></p>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h3>Table of Contents / جدول المحتويات</h3>
            <ul>
                <li><a href="#overview">1. Application Overview / نظرة عامة على التطبيق</a></li>
                <li><a href="#requirements">2. System Requirements / متطلبات النظام</a></li>
                <li><a href="#installation">3. Installation Guide / دليل التثبيت</a></li>
                <li><a href="#architecture">4. Application Architecture / بنية التطبيق</a></li>
                <li><a href="#interface">5. User Interface Guide / دليل واجهة المستخدم</a></li>
                <li><a href="#workflows">6. Step-by-Step Workflows / سير العمل خطوة بخطوة</a></li>
                <li><a href="#technical">7. Technical Implementation / التنفيذ التقني</a></li>
                <li><a href="#troubleshooting">8. Troubleshooting / استكشاف الأخطاء وإصلاحها</a></li>
                <li><a href="#appendices">9. Appendices / الملاحق</a></li>
            </ul>
        </div>

        <!-- Application Overview -->
        <div class="section" id="overview">
            <h2>1. Application Overview / نظرة عامة على التطبيق</h2>
            
            <div class="bilingual">
                <div class="english">
                    <h3>Purpose</h3>
                    <p>PostgreSQL Manager is a comprehensive Windows application designed to provide professional-grade management capabilities for PostgreSQL database servers. The application offers an intuitive graphical interface for database administrators and developers to monitor, control, and interact with PostgreSQL services.</p>
                </div>
                <div class="arabic">
                    <h3>الغرض</h3>
                    <p>يُعد مدير PostgreSQL تطبيق Windows شامل مصمم لتوفير إمكانيات إدارة احترافية لخوادم قواعد بيانات PostgreSQL. يوفر التطبيق واجهة رسومية بديهية لمديري قواعد البيانات والمطورين لمراقبة والتحكم والتفاعل مع خدمات PostgreSQL.</p>
                </div>
            </div>

            <h3>Main Features / الميزات الرئيسية</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4><span class="status-indicator status-success"></span>Service Management / إدارة الخدمة</h4>
                    <ul>
                        <li>Real-time service monitoring / مراقبة الخدمة في الوقت الفعلي</li>
                        <li>Start, stop, restart, and pause operations / عمليات البدء والإيقاف وإعادة التشغيل والإيقاف المؤقت</li>
                        <li>Service status indicators / مؤشرات حالة الخدمة</li>
                        <li>Performance metrics display / عرض مقاييس الأداء</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-info"></span>Database Management / إدارة قواعد البيانات</h4>
                    <ul>
                        <li>Connection testing and management / اختبار وإدارة الاتصالات</li>
                        <li>Database and table browsing / استعراض قواعد البيانات والجداول</li>
                        <li>Schema exploration / استكشاف المخططات</li>
                        <li>Metadata viewing / عرض البيانات الوصفية</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-warning"></span>SQL Editor / محرر SQL</h4>
                    <ul>
                        <li>Advanced SQL query editor / محرر استعلامات SQL متقدم</li>
                        <li>Query execution and results display / تنفيذ الاستعلامات وعرض النتائج</li>
                        <li>Script management / إدارة النصوص البرمجية</li>
                        <li>Syntax highlighting / تمييز بناء الجملة</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4><span class="status-indicator status-error"></span>Configuration Management / إدارة التكوين</h4>
                    <ul>
                        <li>PostgreSQL settings configuration / تكوين إعدادات PostgreSQL</li>
                        <li>Application behavior customization / تخصيص سلوك التطبيق</li>
                        <li>Security settings management / إدارة إعدادات الأمان</li>
                        <li>UI preferences / تفضيلات واجهة المستخدم</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- System Requirements -->
        <div class="section" id="requirements">
            <h2>2. System Requirements / متطلبات النظام</h2>
            
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Component / المكون</th>
                            <th>Minimum / الحد الأدنى</th>
                            <th>Recommended / الموصى به</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Operating System / نظام التشغيل</strong></td>
                            <td>Windows 10 / Windows Server 2016</td>
                            <td>Windows 11 / Windows Server 2022</td>
                        </tr>
                        <tr>
                            <td><strong>CPU / المعالج</strong></td>
                            <td>Intel Core i3 or AMD equivalent</td>
                            <td>Intel Core i5 or AMD Ryzen 5</td>
                        </tr>
                        <tr>
                            <td><strong>RAM / الذاكرة</strong></td>
                            <td>4 GB</td>
                            <td>16 GB or more</td>
                        </tr>
                        <tr>
                            <td><strong>Storage / التخزين</strong></td>
                            <td>500 MB free space</td>
                            <td>SSD with 2 GB free space</td>
                        </tr>
                        <tr>
                            <td><strong>Display / الشاشة</strong></td>
                            <td>1024x768</td>
                            <td>1920x1080 or higher</td>
                        </tr>
                        <tr>
                            <td><strong>.NET Framework</strong></td>
                            <td>4.8 or later</td>
                            <td>Latest version</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Installation Guide -->
        <div class="section" id="installation">
            <h2>3. Installation Guide / دليل التثبيت</h2>
            
            <div class="workflow-step">
                <h5>Step 1: Pre-Installation / الخطوة 1: ما قبل التثبيت</h5>
                <ul>
                    <li>Ensure PostgreSQL Server is installed / تأكد من تثبيت خادم PostgreSQL</li>
                    <li>Verify .NET Framework 4.8+ is installed / تحقق من تثبيت .NET Framework 4.8+</li>
                    <li>Run Windows Update / قم بتشغيل Windows Update</li>
                    <li>Temporarily disable antivirus / عطل مكافح الفيروسات مؤقتاً</li>
                </ul>
            </div>
            
            <div class="workflow-step">
                <h5>Step 2: Installation Process / الخطوة 2: عملية التثبيت</h5>
                <ul>
                    <li>Download PostgreSQL Manager installer / قم بتنزيل مثبت مدير PostgreSQL</li>
                    <li>Right-click and select "Run as Administrator" / انقر بزر الماوس الأيمن واختر "تشغيل كمسؤول"</li>
                    <li>Follow the installation wizard / اتبع معالج التثبيت</li>
                    <li>Choose installation directory / اختر دليل التثبيت</li>
                    <li>Complete the installation / أكمل التثبيت</li>
                </ul>
            </div>
            
            <div class="workflow-step">
                <h5>Step 3: Post-Installation / الخطوة 3: ما بعد التثبيت</h5>
                <div class="code-block">
Default connection settings:
Host: localhost
Port: 5432
Database: postgres
Username: postgres
Password: [as configured during PostgreSQL installation]
                </div>
            </div>
        </div>

        <!-- Application Architecture -->
        <div class="section" id="architecture">
            <h2>4. Application Architecture / بنية التطبيق</h2>

            <h3>High-Level Architecture / البنية عالية المستوى</h3>
            <div class="ui-mockup">
┌─────────────────────────────────────────────────────────┐
│                 Presentation Layer                      │
│                    (WPF Views)                         │
├─────────────────────────────────────────────────────────┤
│                 Business Logic Layer                    │
│                   (ViewModels)                         │
├─────────────────────────────────────────────────────────┤
│                 Service Layer                          │
│    (Database, Service Management, Security)            │
├─────────────────────────────────────────────────────────┤
│                 Data Access Layer                      │
│              (PostgreSQL, Windows APIs)                │
└─────────────────────────────────────────────────────────┘
            </div>

            <h3>Core Components / المكونات الأساسية</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Service Management Layer / طبقة إدارة الخدمة</h4>
                    <ul>
                        <li><strong>PostgreSQLServiceManager:</strong> Core service control / التحكم الأساسي بالخدمة</li>
                        <li><strong>WindowsIntegrationService:</strong> Windows-specific operations / العمليات الخاصة بـ Windows</li>
                        <li><strong>ServiceStatusMonitor:</strong> Real-time monitoring / المراقبة في الوقت الفعلي</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Database Layer / طبقة قاعدة البيانات</h4>
                    <ul>
                        <li><strong>DatabaseService:</strong> Connection management / إدارة الاتصالات</li>
                        <li><strong>QueryExecutor:</strong> SQL execution engine / محرك تنفيذ SQL</li>
                        <li><strong>MetadataProvider:</strong> Schema information / معلومات المخطط</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Security Layer / طبقة الأمان</h4>
                    <ul>
                        <li><strong>AuthenticationService:</strong> User authentication / مصادقة المستخدم</li>
                        <li><strong>EncryptionService:</strong> Data protection / حماية البيانات</li>
                        <li><strong>SecurityManager:</strong> Access control / التحكم في الوصول</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Configuration Layer / طبقة التكوين</h4>
                    <ul>
                        <li><strong>SettingsManager:</strong> Application settings / إعدادات التطبيق</li>
                        <li><strong>ConfigurationProvider:</strong> Runtime configuration / التكوين وقت التشغيل</li>
                        <li><strong>PreferencesStore:</strong> User preferences / تفضيلات المستخدم</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- User Interface Guide -->
        <div class="section" id="interface">
            <h2>5. User Interface Guide / دليل واجهة المستخدم</h2>

            <h3>Main Window Layout / تخطيط النافذة الرئيسية</h3>
            <div class="bilingual">
                <div class="english">
                    <p>The PostgreSQL Manager features a tabbed interface with five main sections:</p>
                </div>
                <div class="arabic">
                    <p>يتميز مدير PostgreSQL بواجهة تبويب مع خمسة أقسام رئيسية:</p>
                </div>
            </div>

            <div class="ui-mockup">
┌─────────────────────────────────────────────────────────┐
│  PostgreSQL Manager - Complete Application             │
├─────────────────────────────────────────────────────────┤
│ [Service Control] [Database Manager] [SQL Editor]      │
│ [Settings] [Test Results]                              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                 Active Tab Content                     │
│                                                         │
│                                                         │
└─────────────────────────────────────────────────────────┘
            </div>

            <!-- Service Control Tab -->
            <h3>Service Control Tab / تبويب التحكم بالخدمة</h3>

            <div class="bilingual">
                <div class="english">
                    <h4>Overview</h4>
                    <p>The Service Control tab provides comprehensive management of the PostgreSQL service with real-time monitoring and control capabilities.</p>
                </div>
                <div class="arabic">
                    <h4>نظرة عامة</h4>
                    <p>يوفر تبويب التحكم بالخدمة إدارة شاملة لخدمة PostgreSQL مع إمكانيات المراقبة والتحكم في الوقت الفعلي.</p>
                </div>
            </div>

            <h4>UI Elements / عناصر واجهة المستخدم</h4>
            <div class="ui-mockup">
┌─────────────────────────────────────────────────────────┐
│ PostgreSQL Service Control                              │
├─────────────────────────────────────────────────────────┤
│ ● Stopped                           [🔄 Refresh]       │
│   postgresql-x64-16                                     │
│                                                         │
│ Uptime: N/A          Memory: N/A                       │
│ CPU: N/A             Process ID: 0                     │
│ Last Checked: 2025-06-21 15:36:27                      │
├─────────────────────────────────────────────────────────┤
│ Service Control                                         │
│                                                         │
│ [Start Service] [Stop Service] [Restart Service]       │
│                 [⏸️ Pause Service]                      │
└─────────────────────────────────────────────────────────┘
            </div>

            <h4>Status Indicators / مؤشرات الحالة</h4>
            <div class="feature-grid">
                <div class="feature-card">
                    <h5><span class="status-indicator status-error"></span>Red Circle / الدائرة الحمراء</h5>
                    <p>Service stopped / الخدمة متوقفة</p>
                </div>
                <div class="feature-card">
                    <h5><span class="status-indicator status-success"></span>Green Circle / الدائرة الخضراء</h5>
                    <p>Service running / الخدمة تعمل</p>
                </div>
                <div class="feature-card">
                    <h5><span class="status-indicator status-warning"></span>Yellow Circle / الدائرة الصفراء</h5>
                    <p>Service transitioning / الخدمة في حالة انتقال</p>
                </div>
                <div class="feature-card">
                    <h5><span class="status-indicator status-info"></span>Gray Circle / الدائرة الرمادية</h5>
                    <p>Service status unknown / حالة الخدمة غير معروفة</p>
                </div>
            </div>

            <h4>Information Fields / حقول المعلومات</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Field / الحقل</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Uptime</strong></td>
                            <td>Time since service started / الوقت منذ بدء الخدمة</td>
                        </tr>
                        <tr>
                            <td><strong>Memory</strong></td>
                            <td>Current memory usage / استخدام الذاكرة الحالي</td>
                        </tr>
                        <tr>
                            <td><strong>CPU</strong></td>
                            <td>Current CPU usage / استخدام المعالج الحالي</td>
                        </tr>
                        <tr>
                            <td><strong>Process ID</strong></td>
                            <td>Windows process identifier / معرف العملية في Windows</td>
                        </tr>
                        <tr>
                            <td><strong>Last Checked</strong></td>
                            <td>Last status update time / وقت آخر تحديث للحالة</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Control Buttons / أزرار التحكم</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Button / الزر</th>
                            <th>Function / الوظيفة</th>
                            <th>Availability / التوفر</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Start Service</strong></td>
                            <td>Starts PostgreSQL service / يبدأ خدمة PostgreSQL</td>
                            <td>When stopped / عند التوقف</td>
                        </tr>
                        <tr>
                            <td><strong>Stop Service</strong></td>
                            <td>Stops PostgreSQL service / يوقف خدمة PostgreSQL</td>
                            <td>When running / عند التشغيل</td>
                        </tr>
                        <tr>
                            <td><strong>Restart Service</strong></td>
                            <td>Restarts PostgreSQL service / يعيد تشغيل خدمة PostgreSQL</td>
                            <td>When running / عند التشغيل</td>
                        </tr>
                        <tr>
                            <td><strong>Pause Service</strong></td>
                            <td>Pauses PostgreSQL service / يوقف خدمة PostgreSQL مؤقتاً</td>
                            <td>When running / عند التشغيل</td>
                        </tr>
                        <tr>
                            <td><strong>Refresh</strong></td>
                            <td>Updates service status / يحدث حالة الخدمة</td>
                            <td>Always / دائماً</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Database Manager Tab -->
            <h3>Database Manager Tab / تبويب إدارة قواعد البيانات</h3>

            <div class="bilingual">
                <div class="english">
                    <h4>Overview</h4>
                    <p>The Database Manager tab provides comprehensive database connectivity and management features, allowing users to connect to PostgreSQL databases, browse schemas, and manage database objects.</p>
                </div>
                <div class="arabic">
                    <h4>نظرة عامة</h4>
                    <p>يوفر تبويب إدارة قواعد البيانات ميزات شاملة لاتصال وإدارة قواعد البيانات، مما يسمح للمستخدمين بالاتصال بقواعد بيانات PostgreSQL واستعراض المخططات وإدارة كائنات قاعدة البيانات.</p>
                </div>
            </div>

            <h4>UI Layout / تخطيط واجهة المستخدم</h4>
            <div class="ui-mockup">
┌─────────────────────────────────────────────────────────┐
│ Database Manager                                        │
├─────────────────────────────────────────────────────────┤
│ Connection Settings                                     │
│ ┌─────────────────┐ ┌─────────────────┐                │
│ │ Host: localhost │ │ Port: 5432      │ [Test Connection]│
│ └─────────────────┘ └─────────────────┘                │
│ ┌─────────────────┐ ┌─────────────────┐                │
│ │ Database: postgres │ │ Username: postgres │          │
│ └─────────────────┘ └─────────────────┘                │
│ ┌─────────────────┐                                     │
│ │ Password: ****  │                    [Connect]       │
│ └─────────────────┘                                     │
├─────────────────────────────────────────────────────────┤
│ Database Browser                                        │
│ ┌─────────────────┐ ┌─────────────────────────────────┐ │
│ │ 📁 Databases    │ │ Table Details                   │ │
│ │ ├─ postgres     │ │                                 │ │
│ │ ├─ template0    │ │ Selected: [table_name]          │ │
│ │ ├─ template1    │ │                                 │ │
│ │ └─ mydb         │ │ Columns: [column_list]          │ │
│ │   ├─ 📋 Tables  │ │                                 │ │
│ │   ├─ 👁️ Views   │ │ Indexes: [index_list]           │ │
│ │   └─ ⚙️ Functions│ │                                 │ │
│ └─────────────────┘ └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
            </div>

            <h4>Connection Management / إدارة الاتصالات</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Parameter / المعامل</th>
                            <th>Default / الافتراضي</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Host</strong></td>
                            <td>localhost</td>
                            <td>Database server address / عنوان خادم قاعدة البيانات</td>
                        </tr>
                        <tr>
                            <td><strong>Port</strong></td>
                            <td>5432</td>
                            <td>PostgreSQL port number / رقم منفذ PostgreSQL</td>
                        </tr>
                        <tr>
                            <td><strong>Database</strong></td>
                            <td>postgres</td>
                            <td>Target database name / اسم قاعدة البيانات المستهدفة</td>
                        </tr>
                        <tr>
                            <td><strong>Username</strong></td>
                            <td>postgres</td>
                            <td>Database user account / حساب مستخدم قاعدة البيانات</td>
                        </tr>
                        <tr>
                            <td><strong>Password</strong></td>
                            <td>[user input]</td>
                            <td>User password / كلمة مرور المستخدم</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="success-box">
                <h5>Connection Testing / اختبار الاتصال</h5>
                <p><strong>English:</strong> The "Test Connection" feature verifies database connectivity without establishing a persistent connection. Click "Test Connection" to verify your settings before connecting.</p>
                <p><strong>العربية:</strong> تتحقق ميزة "Test Connection" من اتصال قاعدة البيانات دون إنشاء اتصال دائم. انقر على "Test Connection" للتحقق من إعداداتك قبل الاتصال.</p>
            </div>

            <h4>Database Browser / متصفح قاعدة البيانات</h4>
            <div class="ui-mockup">
📁 Databases
├─ 📊 postgres (system database)
├─ 📊 template0 (template database)
├─ 📊 template1 (template database)
└─ 📊 [user_databases]
   ├─ 📋 Tables
   │  ├─ 📄 table1
   │  ├─ 📄 table2
   │  └─ 📄 table_n
   ├─ 👁️ Views
   │  ├─ 📄 view1
   │  └─ 📄 view_n
   ├─ ⚙️ Functions
   │  ├─ 🔧 function1
   │  └─ 🔧 function_n
   └─ 🔐 Schemas
      ├─ 📁 public
      └─ 📁 [custom_schemas]
            </div>

            <!-- SQL Editor Tab -->
            <h3>SQL Editor Tab / تبويب محرر SQL</h3>

            <div class="bilingual">
                <div class="english">
                    <h4>Overview</h4>
                    <p>The SQL Editor tab provides a comprehensive environment for writing, executing, and managing SQL queries. It features syntax highlighting, query execution, results display, and script management capabilities.</p>
                </div>
                <div class="arabic">
                    <h4>نظرة عامة</h4>
                    <p>يوفر تبويب محرر SQL بيئة شاملة لكتابة وتنفيذ وإدارة استعلامات SQL. يتميز بتمييز بناء الجملة وتنفيذ الاستعلامات وعرض النتائج وإمكانيات إدارة النصوص البرمجية.</p>
                </div>
            </div>

            <h4>UI Layout / تخطيط واجهة المستخدم</h4>
            <div class="ui-mockup">
┌─────────────────────────────────────────────────────────┐
│ SQL Editor                                              │
├─────────────────────────────────────────────────────────┤
│ Toolbar: [New] [Open] [Save] [Execute] [Clear]         │
├─────────────────────────────────────────────────────────┤
│ Query Editor                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 1  SELECT * FROM users                              │ │
│ │ 2  WHERE active = true                              │ │
│ │ 3  ORDER BY created_date DESC;                      │ │
│ │ 4                                                   │ │
│ │ 5  -- Your SQL queries here                        │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ Results Panel                                           │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Query executed successfully                         │ │
│ │ Rows affected: 25                                   │ │
│ │ Execution time: 0.045 seconds                       │ │
│ │                                                     │ │
│ │ [Results Grid]                                      │ │
│ │ | ID | Name     | Email           | Active |        │ │
│ │ |----|----------|-----------------|--------|        │ │
│ │ | 1  | John Doe | <EMAIL>  | true   |        │ │
│ │ | 2  | Jane Doe | <EMAIL>  | true   |        │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
            </div>

            <h4>Editor Features / ميزات المحرر</h4>
            <div class="feature-grid">
                <div class="feature-card">
                    <h5>Syntax Highlighting / تمييز بناء الجملة</h5>
                    <ul>
                        <li>SQL Keywords (SELECT, FROM, WHERE, etc.) / كلمات SQL المفتاحية</li>
                        <li>Data Types (INTEGER, VARCHAR, etc.) / أنواع البيانات</li>
                        <li>String Literals / النصوص الحرفية</li>
                        <li>Numeric Values / القيم الرقمية</li>
                        <li>Comments (-- and /* */) / التعليقات</li>
                        <li>Functions / الدوال</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h5>Line Numbers / أرقام الأسطر</h5>
                    <ul>
                        <li>Automatic line numbering / ترقيم تلقائي للأسطر</li>
                        <li>Current line highlighting / تمييز السطر الحالي</li>
                        <li>Easy navigation / تنقل سهل</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h5>Code Completion / إكمال الكود</h5>
                    <ul>
                        <li>Table name suggestions / اقتراحات أسماء الجداول</li>
                        <li>Column name completion / إكمال أسماء الأعمدة</li>
                        <li>SQL keyword completion / إكمال كلمات SQL المفتاحية</li>
                        <li>Function suggestions / اقتراحات الدوال</li>
                    </ul>
                </div>
            </div>

            <h4>Toolbar Functions / وظائف شريط الأدوات</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Button / الزر</th>
                            <th>Function / الوظيفة</th>
                            <th>Shortcut / الاختصار</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>New</strong></td>
                            <td>Create new query / إنشاء استعلام جديد</td>
                            <td>Ctrl+N</td>
                        </tr>
                        <tr>
                            <td><strong>Open</strong></td>
                            <td>Open saved script / فتح نص برمجي محفوظ</td>
                            <td>Ctrl+O</td>
                        </tr>
                        <tr>
                            <td><strong>Save</strong></td>
                            <td>Save current script / حفظ النص البرمجي الحالي</td>
                            <td>Ctrl+S</td>
                        </tr>
                        <tr>
                            <td><strong>Execute</strong></td>
                            <td>Run SQL query / تشغيل استعلام SQL</td>
                            <td>F5</td>
                        </tr>
                        <tr>
                            <td><strong>Clear</strong></td>
                            <td>Clear editor content / مسح محتوى المحرر</td>
                            <td>Ctrl+L</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Settings Tab -->
            <h3>Settings Tab / تبويب الإعدادات</h3>

            <div class="bilingual">
                <div class="english">
                    <h4>Overview</h4>
                    <p>The Settings tab provides comprehensive configuration options for the PostgreSQL Manager application, organized into five main categories: PostgreSQL, Application, Security, UI, and Logging settings.</p>
                </div>
                <div class="arabic">
                    <h4>نظرة عامة</h4>
                    <p>يوفر تبويب الإعدادات خيارات تكوين شاملة لتطبيق مدير PostgreSQL، منظمة في خمس فئات رئيسية: إعدادات PostgreSQL والتطبيق والأمان وواجهة المستخدم والسجلات.</p>
                </div>
            </div>

            <h4>PostgreSQL Settings / إعدادات PostgreSQL</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Setting / الإعداد</th>
                            <th>Default / الافتراضي</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Default Host</strong></td>
                            <td>localhost</td>
                            <td>Default database server / خادم قاعدة البيانات الافتراضي</td>
                        </tr>
                        <tr>
                            <td><strong>Default Port</strong></td>
                            <td>5432</td>
                            <td>Default connection port / منفذ الاتصال الافتراضي</td>
                        </tr>
                        <tr>
                            <td><strong>Default Database</strong></td>
                            <td>postgres</td>
                            <td>Default database name / اسم قاعدة البيانات الافتراضي</td>
                        </tr>
                        <tr>
                            <td><strong>Default Username</strong></td>
                            <td>postgres</td>
                            <td>Default user account / حساب المستخدم الافتراضي</td>
                        </tr>
                        <tr>
                            <td><strong>Connection Timeout</strong></td>
                            <td>30 seconds</td>
                            <td>Connection timeout period / فترة انتهاء مهلة الاتصال</td>
                        </tr>
                        <tr>
                            <td><strong>Command Timeout</strong></td>
                            <td>30 seconds</td>
                            <td>Query execution timeout / انتهاء مهلة تنفيذ الاستعلام</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Application Settings / إعدادات التطبيق</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Setting / الإعداد</th>
                            <th>Default / الافتراضي</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Start with Windows</strong></td>
                            <td>false</td>
                            <td>Launch app with Windows / تشغيل التطبيق مع Windows</td>
                        </tr>
                        <tr>
                            <td><strong>Auto Start Service</strong></td>
                            <td>false</td>
                            <td>Automatically start PostgreSQL / بدء PostgreSQL تلقائياً</td>
                        </tr>
                        <tr>
                            <td><strong>Minimize to Tray</strong></td>
                            <td>true</td>
                            <td>Minimize to system tray / تصغير إلى علبة النظام</td>
                        </tr>
                        <tr>
                            <td><strong>Check for Updates</strong></td>
                            <td>true</td>
                            <td>Automatic update checking / فحص التحديثات التلقائي</td>
                        </tr>
                        <tr>
                            <td><strong>Auto Save Interval</strong></td>
                            <td>5 minutes</td>
                            <td>Automatic save frequency / تكرار الحفظ التلقائي</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Security Settings / إعدادات الأمان</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Setting / الإعداد</th>
                            <th>Default / الافتراضي</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Encryption Enabled</strong></td>
                            <td>false</td>
                            <td>Enable data encryption / تفعيل تشفير البيانات</td>
                        </tr>
                        <tr>
                            <td><strong>Require Master Password</strong></td>
                            <td>false</td>
                            <td>Require master password / طلب كلمة مرور رئيسية</td>
                        </tr>
                        <tr>
                            <td><strong>Session Timeout</strong></td>
                            <td>60 minutes</td>
                            <td>Session timeout period / فترة انتهاء الجلسة</td>
                        </tr>
                        <tr>
                            <td><strong>Max Login Attempts</strong></td>
                            <td>3</td>
                            <td>Maximum failed login attempts / الحد الأقصى لمحاولات تسجيل الدخول الفاشلة</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>UI Settings / إعدادات واجهة المستخدم</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Setting / الإعداد</th>
                            <th>Default / الافتراضي</th>
                            <th>Description / الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Theme</strong></td>
                            <td>Light</td>
                            <td>Application theme / موضوع التطبيق</td>
                        </tr>
                        <tr>
                            <td><strong>Language</strong></td>
                            <td>en-US</td>
                            <td>Interface language / لغة الواجهة</td>
                        </tr>
                        <tr>
                            <td><strong>Font Family</strong></td>
                            <td>Consolas</td>
                            <td>Editor font family / عائلة خط المحرر</td>
                        </tr>
                        <tr>
                            <td><strong>Font Size</strong></td>
                            <td>12</td>
                            <td>Editor font size / حجم خط المحرر</td>
                        </tr>
                        <tr>
                            <td><strong>Show Line Numbers</strong></td>
                            <td>true</td>
                            <td>Display line numbers / عرض أرقام الأسطر</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Test Results Tab -->
            <h3>Test Results Tab / تبويب نتائج الاختبار</h3>

            <div class="bilingual">
                <div class="english">
                    <h4>Overview</h4>
                    <p>The Test Results tab provides comprehensive testing and diagnostic capabilities for the PostgreSQL Manager application. It displays detailed information about all system components and their operational status.</p>
                </div>
                <div class="arabic">
                    <h4>نظرة عامة</h4>
                    <p>يوفر تبويب نتائج الاختبار إمكانيات اختبار وتشخيص شاملة لتطبيق مدير PostgreSQL. يعرض معلومات مفصلة حول جميع مكونات النظام وحالتها التشغيلية.</p>
                </div>
            </div>

            <h4>Core Services Testing / اختبار الخدمات الأساسية</h4>
            <div class="code-block">
✅ CORE SERVICES (Phase 1):
✅ EncryptionService: EncryptionService
✅ AuthenticationService: AuthenticationService
✅ DatabaseService: DatabaseService
✅ PostgreSQLServiceManager: PostgreSQLServiceManager
✅ ScriptManagementService: ScriptManagementService
            </div>

            <h4>ViewModels Testing / اختبار ViewModels</h4>
            <div class="code-block">
✅ VIEW MODELS (Phase 2):
✅ ServiceControlViewModel: ServiceControlViewModel
✅ DatabaseManagerViewModel: DatabaseManagerViewModel
✅ SqlEditorViewModel: SqlEditorViewModel
✅ SettingsViewModel: SettingsViewModel (Simplified)
            </div>

            <h4>Views Testing / اختبار Views</h4>
            <div class="code-block">
✅ VIEWS (Phase 2.3):
✅ ServiceControlView: ServiceControlView
✅ DatabaseManagerView: DatabaseManagerView
✅ SqlEditorView: SqlEditorView
✅ SettingsView: SettingsView
            </div>

            <h4>Advanced Features Testing / اختبار الميزات المتقدمة</h4>
            <div class="code-block">
✅ ADVANCED FEATURES (Phase 3-4):
✅ NLog Configuration: Loaded
✅ WindowsIntegrationService: Fully Functional
✅ SettingsView: Simplified & Functional
            </div>

            <h4>Test Results Interpretation / تفسير نتائج الاختبار</h4>
            <div class="feature-grid">
                <div class="feature-card">
                    <h5><span class="status-indicator status-success"></span>Success Indicators / مؤشرات النجاح</h5>
                    <ul>
                        <li>Green Checkmark: Component working correctly / المكون يعمل بشكل صحيح</li>
                        <li>Component Name: Displays actual implementation / يعرض التنفيذ الفعلي</li>
                        <li>Status Message: Confirms successful operation / يؤكد العملية الناجحة</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h5><span class="status-indicator status-warning"></span>Warning Indicators / مؤشرات التحذير</h5>
                    <ul>
                        <li>Yellow Warning: Component has limitations / المكون له قيود</li>
                        <li>"Simplified" Label: Reduced functionality / وظائف مخفضة</li>
                        <li>"Temporarily Disabled": Feature not active / الميزة غير نشطة</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h5><span class="status-indicator status-error"></span>Error Indicators / مؤشرات الخطأ</h5>
                    <ul>
                        <li>Red X: Component failed / فشل المكون</li>
                        <li>Error Message: Specific failure reason / سبب الفشل المحدد</li>
                        <li>"Not Available": Component not accessible / المكون غير متاح</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Step-by-Step Workflows -->
        <div class="section" id="workflows">
            <h2>6. Step-by-Step Workflows / سير العمل خطوة بخطوة</h2>

            <!-- Starting PostgreSQL Service -->
            <h3>Starting PostgreSQL Service / بدء خدمة PostgreSQL</h3>

            <div class="info-box">
                <h4>Prerequisites / المتطلبات المسبقة</h4>
                <ul>
                    <li>PostgreSQL installed on the system / PostgreSQL مثبت على النظام</li>
                    <li>PostgreSQL Manager application running / تطبيق مدير PostgreSQL يعمل</li>
                    <li>Administrative privileges (if required) / امتيازات إدارية (إذا لزم الأمر)</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 1: Open PostgreSQL Manager / الخطوة 1: افتح مدير PostgreSQL</h5>
                <ul>
                    <li>Launch the application / شغل التطبيق</li>
                    <li>Wait for complete loading / انتظر التحميل الكامل</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 2: Navigate to Service Control Tab / الخطوة 2: انتقل إلى تبويب التحكم بالخدمة</h5>
                <ul>
                    <li>Click on "Service Control" tab / انقر على تبويب "Service Control"</li>
                    <li>Verify tab is active / تحقق من أن التبويب نشط</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 3: Check Current Service Status / الخطوة 3: تحقق من حالة الخدمة الحالية</h5>
                <ul>
                    <li>Observe status indicator / لاحظ مؤشر الحالة</li>
                    <li>Red circle = Stopped / الدائرة الحمراء = متوقف</li>
                    <li>Green circle = Running / الدائرة الخضراء = يعمل</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 4: Start the Service / الخطوة 4: ابدأ الخدمة</h5>
                <ul>
                    <li>Click "Start Service" button / انقر على زر "Start Service"</li>
                    <li>Button should be enabled only when service is stopped / يجب أن يكون الزر مفعلاً فقط عند توقف الخدمة</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 5: Monitor Startup Process / الخطوة 5: راقب عملية بدء التشغيل</h5>
                <ul>
                    <li>Status changes to "Starting" / تتغير الحالة إلى "Starting"</li>
                    <li>Wait for status to become "Running" / انتظر حتى تصبح الحالة "Running"</li>
                    <li>Green indicator appears / يظهر المؤشر الأخضر</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 6: Verify Successful Startup / الخطوة 6: تحقق من نجاح بدء التشغيل</h5>
                <ul>
                    <li>Status shows "Running" / الحالة تظهر "Running"</li>
                    <li>Process ID is displayed / معرف العملية معروض</li>
                    <li>Uptime counter starts / عداد وقت التشغيل يبدأ</li>
                </ul>
            </div>

            <!-- Connecting to Database -->
            <h3>Connecting to Database / الاتصال بقاعدة البيانات</h3>

            <div class="info-box">
                <h4>Prerequisites / المتطلبات المسبقة</h4>
                <ul>
                    <li>PostgreSQL service running / خدمة PostgreSQL تعمل</li>
                    <li>Valid database credentials / بيانات اعتماد صحيحة لقاعدة البيانات</li>
                    <li>Network connectivity (if remote) / اتصال الشبكة (إذا كان عن بُعد)</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 1: Navigate to Database Manager Tab / الخطوة 1: انتقل إلى تبويب إدارة قواعد البيانات</h5>
                <ul>
                    <li>Click "Database Manager" tab / انقر على تبويب "Database Manager"</li>
                    <li>Connection form appears / يظهر نموذج الاتصال</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 2: Configure Connection Parameters / الخطوة 2: قم بتكوين معاملات الاتصال</h5>
                <ul>
                    <li><strong>Host Configuration:</strong> Enter server address (Default: localhost) / أدخل عنوان الخادم (الافتراضي: localhost)</li>
                    <li><strong>Port Configuration:</strong> Enter port number (Default: 5432) / أدخل رقم المنفذ (الافتراضي: 5432)</li>
                    <li><strong>Database Selection:</strong> Enter database name (Default: postgres) / أدخل اسم قاعدة البيانات (الافتراضي: postgres)</li>
                    <li><strong>Authentication:</strong> Enter username and password / أدخل اسم المستخدم وكلمة المرور</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 3: Test Connection / الخطوة 3: اختبر الاتصال</h5>
                <ul>
                    <li>Click "Test Connection" button / انقر على زر "Test Connection"</li>
                    <li>Wait for result message / انتظر رسالة النتيجة</li>
                    <li>Success: "Connection test successful" / النجاح: "Connection test successful"</li>
                    <li>Failure: Error message with details / الفشل: رسالة خطأ مع التفاصيل</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 4: Establish Connection / الخطوة 4: أنشئ الاتصال</h5>
                <ul>
                    <li>Click "Connect" button / انقر على زر "Connect"</li>
                    <li>Wait for database browser to populate / انتظر ملء متصفح قاعدة البيانات</li>
                    <li>Database tree structure appears / يظهر هيكل شجرة قاعدة البيانات</li>
                </ul>
            </div>

            <!-- Writing and Executing SQL Queries -->
            <h3>Writing and Executing SQL Queries / كتابة وتنفيذ استعلامات SQL</h3>

            <div class="info-box">
                <h4>Prerequisites / المتطلبات المسبقة</h4>
                <ul>
                    <li>Active database connection / اتصال نشط بقاعدة البيانات</li>
                    <li>Basic SQL knowledge / معرفة أساسية بـ SQL</li>
                    <li>Appropriate database permissions / أذونات مناسبة لقاعدة البيانات</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 1: Navigate to SQL Editor Tab / الخطوة 1: انتقل إلى تبويب محرر SQL</h5>
                <ul>
                    <li>Click "SQL Editor" tab / انقر على تبويب "SQL Editor"</li>
                    <li>Editor interface loads / تحميل واجهة المحرر</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 2: Prepare the Editor / الخطوة 2: أعد المحرر</h5>
                <ul>
                    <li>Clear existing content (if needed) / امسح المحتوى الموجود (إذا لزم الأمر)</li>
                    <li>Click "Clear" button or Ctrl+L / انقر على زر "Clear" أو Ctrl+L</li>
                    <li>Cursor positioned at line 1 / المؤشر موضوع في السطر 1</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 3: Write SQL Query / الخطوة 3: اكتب استعلام SQL</h5>
                <div class="code-block">
-- Basic SELECT Query / استعلام SELECT أساسي
SELECT * FROM users
WHERE active = true
ORDER BY created_date DESC;

-- INSERT Query / استعلام INSERT
INSERT INTO users (name, email, active)
VALUES ('John Doe', '<EMAIL>', true);

-- UPDATE Query / استعلام UPDATE
UPDATE users
SET last_login = NOW()
WHERE id = 1;

-- DELETE Query / استعلام DELETE
DELETE FROM users
WHERE active = false
AND last_login < '2023-01-01';
                </div>
            </div>

            <div class="workflow-step">
                <h5>Step 4: Execute the Query / الخطوة 4: نفذ الاستعلام</h5>
                <ul>
                    <li>Click "Execute" button or press F5 / انقر على زر "Execute" أو اضغط F5</li>
                    <li>Query is sent to database / يتم إرسال الاستعلام إلى قاعدة البيانات</li>
                    <li>Wait for execution to complete / انتظر اكتمال التنفيذ</li>
                </ul>
            </div>

            <div class="workflow-step">
                <h5>Step 5: Review Results / الخطوة 5: راجع النتائج</h5>
                <div class="success-box">
                    <h6>Successful Execution / التنفيذ الناجح</h6>
                    <ul>
                        <li>Results appear in results panel / تظهر النتائج في لوحة النتائج</li>
                        <li>Data grid shows query results / شبكة البيانات تظهر نتائج الاستعلام</li>
                        <li>Status shows "Query executed successfully" / الحالة تظهر "Query executed successfully"</li>
                        <li>Execution time is displayed / وقت التنفيذ معروض</li>
                    </ul>
                </div>
                <div class="troubleshooting">
                    <h6>Error Handling / معالجة الأخطاء</h6>
                    <ul>
                        <li>Error message appears in results panel / تظهر رسالة خطأ في لوحة النتائج</li>
                        <li>Error details include line number / تفاصيل الخطأ تتضمن رقم السطر</li>
                        <li>Suggested corrections may be provided / قد يتم توفير تصحيحات مقترحة</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Technical Implementation -->
        <div class="section" id="technical">
            <h2>7. Technical Implementation / التنفيذ التقني</h2>

            <!-- Service Management Architecture -->
            <h3>Service Management Architecture / بنية إدارة الخدمة</h3>

            <h4>PostgreSQLServiceManager</h4>
            <div class="bilingual">
                <div class="english">
                    <p>The PostgreSQLServiceManager is the central component responsible for all PostgreSQL service operations:</p>
                </div>
                <div class="arabic">
                    <p>مدير خدمة PostgreSQL هو المكون المركزي المسؤول عن جميع عمليات خدمة PostgreSQL:</p>
                </div>
            </div>

            <div class="code-block">
public class PostgreSQLServiceManager : IPostgreSQLServiceManager
{
    // Service control operations
    public async Task&lt;bool&gt; StartServiceAsync()
    public async Task&lt;bool&gt; StopServiceAsync()
    public async Task&lt;bool&gt; RestartServiceAsync()
    public async Task&lt;bool&gt; PauseServiceAsync()

    // Status monitoring
    public ServiceStatus GetServiceStatus()
    public bool IsServiceRunning()
    public ServiceInfo GetServiceInfo()
}
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h5>Key Features / الميزات الرئيسية</h5>
                    <ul>
                        <li>Asynchronous operations / العمليات غير المتزامنة</li>
                        <li>Real-time status monitoring / مراقبة الحالة في الوقت الفعلي</li>
                        <li>Error handling and recovery / معالجة الأخطاء والاستعادة</li>
                        <li>Performance metrics collection / جمع مقاييس الأداء</li>
                    </ul>
                </div>
            </div>

            <h4>WindowsIntegrationService</h4>
            <div class="bilingual">
                <div class="english">
                    <p>Provides Windows-specific functionality and system integration:</p>
                </div>
                <div class="arabic">
                    <p>يوفر وظائف خاصة بـ Windows وتكامل النظام:</p>
                </div>
            </div>

            <div class="code-block">
public class WindowsIntegrationService : IWindowsIntegrationService
{
    // Windows service integration
    public void RegisterStartupApplication()
    public void CreateDesktopShortcut()
    public void ConfigureSystemTray()

    // System monitoring
    public SystemInfo GetSystemInformation()
    public PerformanceMetrics GetPerformanceMetrics()
}
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h5>Capabilities / القدرات</h5>
                    <ul>
                        <li>Startup registration / تسجيل بدء التشغيل</li>
                        <li>Desktop shortcuts / اختصارات سطح المكتب</li>
                        <li>System tray integration / تكامل علبة النظام</li>
                        <li>Performance monitoring / مراقبة الأداء</li>
                    </ul>
                </div>
            </div>

            <!-- Database Connectivity Framework -->
            <h3>Database Connectivity Framework / إطار عمل اتصال قاعدة البيانات</h3>

            <h4>DatabaseService Architecture / بنية خدمة قاعدة البيانات</h4>
            <div class="code-block">
public class DatabaseService : IDatabaseService
{
    // Connection operations
    public async Task&lt;bool&gt; TestConnectionAsync(ConnectionInfo connectionInfo)
    public async Task&lt;IDbConnection&gt; CreateConnectionAsync(ConnectionInfo connectionInfo)
    public async Task&lt;bool&gt; ValidateConnectionAsync(IDbConnection connection)

    // Query execution
    public async Task&lt;DataTable&gt; ExecuteQueryAsync(string query)
    public async Task&lt;int&gt; ExecuteNonQueryAsync(string query)
    public async Task&lt;object&gt; ExecuteScalarAsync(string query)
}
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h5>Features / الميزات</h5>
                    <ul>
                        <li>Connection pooling / تجميع الاتصالات</li>
                        <li>Automatic retry logic / منطق إعادة المحاولة التلقائي</li>
                        <li>Transaction support / دعم المعاملات</li>
                        <li>Query optimization / تحسين الاستعلامات</li>
                    </ul>
                </div>
            </div>

            <h4>MetadataProvider / موفر البيانات الوصفية</h4>
            <div class="code-block">
public class MetadataProvider : IMetadataProvider
{
    // Schema information
    public async Task&lt;List&lt;DatabaseInfo&gt;&gt; GetDatabasesAsync()
    public async Task&lt;List&lt;TableInfo&gt;&gt; GetTablesAsync(string database)
    public async Task&lt;List&lt;ColumnInfo&gt;&gt; GetColumnsAsync(string table)
    public async Task&lt;List&lt;IndexInfo&gt;&gt; GetIndexesAsync(string table)

    // Object details
    public async Task&lt;TableDefinition&gt; GetTableDefinitionAsync(string table)
    public async Task&lt;ViewDefinition&gt; GetViewDefinitionAsync(string view)
}
            </div>

            <!-- Security Implementation -->
            <h3>Security Implementation / تنفيذ الأمان</h3>

            <h4>Authentication Service / خدمة المصادقة</h4>
            <div class="code-block">
public class AuthenticationService : IAuthenticationService
{
    // Authentication operations
    public async Task&lt;AuthenticationResult&gt; AuthenticateAsync(string username, string password)
    public async Task&lt;bool&gt; ValidateSessionAsync(string sessionToken)
    public async Task&lt;bool&gt; ChangePasswordAsync(string oldPassword, string newPassword)

    // Session management
    public async Task&lt;string&gt; CreateSessionAsync(string username)
    public async Task&lt;bool&gt; InvalidateSessionAsync(string sessionToken)
}
            </div>

            <h4>Encryption Service / خدمة التشفير</h4>
            <div class="code-block">
public class EncryptionService : IEncryptionService
{
    // Encryption operations
    public string EncryptString(string plainText, string key)
    public string DecryptString(string cipherText, string key)
    public byte[] EncryptData(byte[] data, string key)
    public byte[] DecryptData(byte[] encryptedData, string key)

    // Key management
    public string GenerateKey()
    public bool ValidateKey(string key)
}
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h5>Security Features / ميزات الأمان</h5>
                    <ul>
                        <li>AES-256 encryption / تشفير AES-256</li>
                        <li>Windows DPAPI integration / تكامل Windows DPAPI</li>
                        <li>Secure key storage / تخزين آمن للمفاتيح</li>
                        <li>Data integrity verification / التحقق من سلامة البيانات</li>
                    </ul>
                </div>
            </div>

            <!-- Error Handling and Logging -->
            <h3>Error Handling and Logging / معالجة الأخطاء والتسجيل</h3>

            <h4>NLog Integration / تكامل NLog</h4>
            <div class="code-block">
&lt;nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"&gt;
  &lt;targets&gt;
    &lt;target xsi:type="File" name="fileTarget"
            fileName="Logs/PostgreSQLManager-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" /&gt;
    &lt;target xsi:type="Console" name="consoleTarget"
            layout="${time} [${level}] ${message}" /&gt;
  &lt;/targets&gt;

  &lt;rules&gt;
    &lt;logger name="*" minlevel="Info" writeTo="fileTarget" /&gt;
    &lt;logger name="*" minlevel="Debug" writeTo="consoleTarget" /&gt;
  &lt;/rules&gt;
&lt;/nlog&gt;
            </div>

            <h4>Logging Service / خدمة التسجيل</h4>
            <div class="code-block">
public class LoggingService : ILoggingService
{
    // Logging operations
    public void LogInfo(string message, params object[] args)
    public void LogWarning(string message, params object[] args)
    public void LogError(string message, Exception exception = null)
    public void LogDebug(string message, params object[] args)

    // Performance logging
    public void LogPerformance(string operation, TimeSpan duration)
    public void LogQuery(string query, TimeSpan executionTime)
}
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="section" id="troubleshooting">
            <h2>8. Troubleshooting / استكشاف الأخطاء وإصلاحها</h2>

            <!-- Application Startup Issues -->
            <h3>Application Startup Issues / مشاكل بدء تشغيل التطبيق</h3>

            <div class="troubleshooting">
                <h4>Issue: Application Won't Start / المشكلة: التطبيق لا يبدأ</h4>

                <h5>Symptoms / الأعراض:</h5>
                <ul>
                    <li>Application crashes on startup / التطبيق ينهار عند بدء التشغيل</li>
                    <li>Error message about missing dependencies / رسالة خطأ حول التبعيات المفقودة</li>
                    <li>Application window doesn't appear / نافذة التطبيق لا تظهر</li>
                </ul>

                <h5>Solutions / الحلول:</h5>
                <div class="workflow-step">
                    <h6>1. Check .NET Framework / تحقق من .NET Framework</h6>
                    <div class="code-block">
# Verify .NET Framework 4.8 is installed
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release
                    </div>
                </div>

                <div class="workflow-step">
                    <h6>2. Run as Administrator / شغل كمسؤول</h6>
                    <ul>
                        <li>Right-click application / انقر بزر الماوس الأيمن على التطبيق</li>
                        <li>Select "Run as Administrator" / اختر "Run as Administrator"</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>3. Check Event Logs / تحقق من سجلات الأحداث</h6>
                    <div class="code-block">
# Open Event Viewer
eventvwr.msc
# Navigate to: Windows Logs > Application
                    </div>
                </div>

                <div class="workflow-step">
                    <h6>4. Verify Dependencies / تحقق من التبعيات</h6>
                    <ul>
                        <li>Ensure PostgreSQL is installed / تأكد من تثبيت PostgreSQL</li>
                        <li>Check Visual C++ Redistributable / تحقق من Visual C++ Redistributable</li>
                        <li>Verify all DLL files are present / تحقق من وجود جميع ملفات DLL</li>
                    </ul>
                </div>
            </div>

            <div class="troubleshooting">
                <h4>Issue: Slow Application Startup / المشكلة: بدء تشغيل بطيء للتطبيق</h4>

                <h5>Symptoms / الأعراض:</h5>
                <ul>
                    <li>Application takes long time to load / التطبيق يستغرق وقتاً طويلاً للتحميل</li>
                    <li>Splash screen appears for extended period / شاشة البداية تظهر لفترة طويلة</li>
                    <li>UI becomes unresponsive during startup / واجهة المستخدم تصبح غير مستجيبة أثناء بدء التشغيل</li>
                </ul>

                <h5>Solutions / الحلول:</h5>
                <div class="workflow-step">
                    <h6>1. Disable Antivirus Scanning / عطل فحص مكافح الفيروسات</h6>
                    <ul>
                        <li>Add application to antivirus exclusions / أضف التطبيق إلى استثناءات مكافح الفيروسات</li>
                        <li>Temporarily disable real-time protection / عطل الحماية في الوقت الفعلي مؤقتاً</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>2. Check System Resources / تحقق من موارد النظام</h6>
                    <ul>
                        <li>Close unnecessary applications / أغلق التطبيقات غير الضرورية</li>
                        <li>Check available RAM and CPU usage / تحقق من ذاكرة الوصول العشوائي المتاحة واستخدام المعالج</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>3. Optimize Startup Settings / حسن إعدادات بدء التشغيل</h6>
                    <ul>
                        <li>Disable auto-start service option / عطل خيار بدء الخدمة التلقائي</li>
                        <li>Reduce startup checks / قلل فحوصات بدء التشغيل</li>
                    </ul>
                </div>
            </div>

            <!-- Service Control Issues -->
            <h3>Service Control Issues / مشاكل التحكم بالخدمة</h3>

            <div class="troubleshooting">
                <h4>Issue: Cannot Start PostgreSQL Service / المشكلة: لا يمكن بدء خدمة PostgreSQL</h4>

                <h5>Symptoms / الأعراض:</h5>
                <ul>
                    <li>"Start Service" button doesn't work / زر "Start Service" لا يعمل</li>
                    <li>Service status remains "Stopped" / حالة الخدمة تبقى "Stopped"</li>
                    <li>Error messages about service access / رسائل خطأ حول الوصول للخدمة</li>
                </ul>

                <h5>Solutions / الحلول:</h5>
                <div class="workflow-step">
                    <h6>1. Check Service Permissions / تحقق من أذونات الخدمة</h6>
                    <div class="code-block">
# Run Command Prompt as Administrator
sc query postgresql-x64-16
sc start postgresql-x64-16
                    </div>
                </div>

                <div class="workflow-step">
                    <h6>2. Verify PostgreSQL Installation / تحقق من تثبيت PostgreSQL</h6>
                    <ul>
                        <li>Check installation directory exists / تحقق من وجود دليل التثبيت</li>
                        <li>Verify postgres.exe is present / تحقق من وجود postgres.exe</li>
                        <li>Check data directory permissions / تحقق من أذونات دليل البيانات</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>3. Check Port Availability / تحقق من توفر المنفذ</h6>
                    <div class="code-block">
# Check if port 5432 is in use
netstat -an | findstr :5432
                    </div>
                </div>

                <div class="workflow-step">
                    <h6>4. Review PostgreSQL Logs / راجع سجلات PostgreSQL</h6>
                    <ul>
                        <li>Check PostgreSQL log directory / تحقق من دليل سجلات PostgreSQL</li>
                        <li>Look for error messages / ابحث عن رسائل الخطأ</li>
                        <li>Check for configuration issues / تحقق من مشاكل التكوين</li>
                    </ul>
                </div>
            </div>

            <!-- Database Connection Issues -->
            <h3>Database Connection Issues / مشاكل اتصال قاعدة البيانات</h3>

            <div class="troubleshooting">
                <h4>Issue: Connection Test Fails / المشكلة: اختبار الاتصال يفشل</h4>

                <h5>Symptoms / الأعراض:</h5>
                <ul>
                    <li>"Connection test failed" message / رسالة "Connection test failed"</li>
                    <li>Timeout errors / أخطاء انتهاء المهلة</li>
                    <li>Authentication failures / فشل المصادقة</li>
                </ul>

                <h5>Solutions / الحلول:</h5>
                <div class="workflow-step">
                    <h6>1. Verify Connection Parameters / تحقق من معاملات الاتصال</h6>
                    <ul>
                        <li>Double-check host, port, database name / تحقق مرة أخرى من المضيف والمنفذ واسم قاعدة البيانات</li>
                        <li>Verify username and password / تحقق من اسم المستخدم وكلمة المرور</li>
                        <li>Test with psql command line / اختبر باستخدام سطر أوامر psql</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>2. Check PostgreSQL Configuration / تحقق من تكوين PostgreSQL</h6>
                    <div class="code-block">
-- Check postgresql.conf
SHOW listen_addresses;
SHOW port;

-- Check pg_hba.conf for authentication rules
                    </div>
                </div>

                <div class="workflow-step">
                    <h6>3. Network Troubleshooting / استكشاف مشاكل الشبكة</h6>
                    <div class="code-block">
# Test network connectivity
ping localhost
telnet localhost 5432
                    </div>
                </div>
            </div>

            <!-- SQL Editor Issues -->
            <h3>SQL Editor Issues / مشاكل محرر SQL</h3>

            <div class="troubleshooting">
                <h4>Issue: Query Execution Fails / المشكلة: تنفيذ الاستعلام يفشل</h4>

                <h5>Symptoms / الأعراض:</h5>
                <ul>
                    <li>SQL queries return errors / استعلامات SQL ترجع أخطاء</li>
                    <li>Syntax highlighting not working / تمييز بناء الجملة لا يعمل</li>
                    <li>Results not displaying / النتائج لا تظهر</li>
                </ul>

                <h5>Solutions / الحلول:</h5>
                <div class="workflow-step">
                    <h6>1. Verify SQL Syntax / تحقق من بناء جملة SQL</h6>
                    <ul>
                        <li>Check for typos and syntax errors / تحقق من الأخطاء الإملائية وأخطاء بناء الجملة</li>
                        <li>Test query in psql first / اختبر الاستعلام في psql أولاً</li>
                        <li>Use simpler queries for testing / استخدم استعلامات أبسط للاختبار</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>2. Check Database Connection / تحقق من اتصال قاعدة البيانات</h6>
                    <ul>
                        <li>Ensure connection is active / تأكد من أن الاتصال نشط</li>
                        <li>Test connection in Database Manager tab / اختبر الاتصال في تبويب إدارة قواعد البيانات</li>
                        <li>Reconnect if necessary / أعد الاتصال إذا لزم الأمر</li>
                    </ul>
                </div>

                <div class="workflow-step">
                    <h6>3. Review Query Permissions / راجع أذونات الاستعلام</h6>
                    <ul>
                        <li>Check user permissions for target tables / تحقق من أذونات المستخدم للجداول المستهدفة</li>
                        <li>Verify database access rights / تحقق من حقوق الوصول لقاعدة البيانات</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Appendices -->
        <div class="section" id="appendices">
            <h2>9. Appendices / الملاحق</h2>

            <!-- Appendix A: Keyboard Shortcuts -->
            <h3>Appendix A: Keyboard Shortcuts / الملحق أ: اختصارات لوحة المفاتيح</h3>

            <h4>Global Shortcuts / الاختصارات العامة</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Shortcut / الاختصار</th>
                            <th>Function / الوظيفة</th>
                            <th>Context / السياق</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Ctrl + N</strong></td>
                            <td>New query / استعلام جديد</td>
                            <td>SQL Editor</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + O</strong></td>
                            <td>Open script / فتح نص برمجي</td>
                            <td>SQL Editor</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + S</strong></td>
                            <td>Save script / حفظ نص برمجي</td>
                            <td>SQL Editor</td>
                        </tr>
                        <tr>
                            <td><strong>F5</strong></td>
                            <td>Execute query / تنفيذ استعلام</td>
                            <td>SQL Editor</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + L</strong></td>
                            <td>Clear editor / مسح المحرر</td>
                            <td>SQL Editor</td>
                        </tr>
                        <tr>
                            <td><strong>F1</strong></td>
                            <td>Help / مساعدة</td>
                            <td>Global</td>
                        </tr>
                        <tr>
                            <td><strong>Alt + F4</strong></td>
                            <td>Exit application / إنهاء التطبيق</td>
                            <td>Global</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + Tab</strong></td>
                            <td>Switch tabs / تبديل التبويبات</td>
                            <td>Global</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>SQL Editor Shortcuts / اختصارات محرر SQL</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Shortcut / الاختصار</th>
                            <th>Function / الوظيفة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Ctrl + A</strong></td>
                            <td>Select all text / تحديد كل النص</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + C</strong></td>
                            <td>Copy selected text / نسخ النص المحدد</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + V</strong></td>
                            <td>Paste text / لصق النص</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + X</strong></td>
                            <td>Cut selected text / قص النص المحدد</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + Z</strong></td>
                            <td>Undo / تراجع</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + Y</strong></td>
                            <td>Redo / إعادة</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + F</strong></td>
                            <td>Find text / البحث عن نص</td>
                        </tr>
                        <tr>
                            <td><strong>Ctrl + H</strong></td>
                            <td>Replace text / استبدال النص</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Appendix B: Configuration Files -->
            <h3>Appendix B: Configuration Files / الملحق ب: ملفات التكوين</h3>

            <h4>App.config Structure / هيكل App.config</h4>
            <div class="code-block">
&lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;configuration&gt;
  &lt;appSettings&gt;
    &lt;!-- PostgreSQL Settings --&gt;
    &lt;add key="DefaultHost" value="localhost" /&gt;
    &lt;add key="DefaultPort" value="5432" /&gt;
    &lt;add key="DefaultDatabase" value="postgres" /&gt;
    &lt;add key="DefaultUsername" value="postgres" /&gt;

    &lt;!-- Application Settings --&gt;
    &lt;add key="StartWithWindows" value="false" /&gt;
    &lt;add key="AutoStartService" value="false" /&gt;
    &lt;add key="MinimizeToTray" value="true" /&gt;

    &lt;!-- Security Settings --&gt;
    &lt;add key="EncryptionEnabled" value="false" /&gt;
    &lt;add key="SessionTimeout" value="60" /&gt;

    &lt;!-- UI Settings --&gt;
    &lt;add key="Theme" value="Light" /&gt;
    &lt;add key="Language" value="en-US" /&gt;
    &lt;add key="FontFamily" value="Consolas" /&gt;
    &lt;add key="FontSize" value="12" /&gt;
  &lt;/appSettings&gt;

  &lt;connectionStrings&gt;
    &lt;add name="DefaultConnection"
         connectionString="Host=localhost;Port=5432;Database=postgres;Username=postgres;" /&gt;
  &lt;/connectionStrings&gt;
&lt;/configuration&gt;
            </div>

            <h4>NLog.config Structure / هيكل NLog.config</h4>
            <div class="code-block">
&lt;?xml version="1.0" encoding="utf-8" ?&gt;
&lt;nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;

  &lt;targets&gt;
    &lt;!-- File Target --&gt;
    &lt;target xsi:type="File" name="fileTarget"
            fileName="Logs/PostgreSQLManager-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="Logs/Archive/PostgreSQLManager-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30" /&gt;

    &lt;!-- Console Target --&gt;
    &lt;target xsi:type="Console" name="consoleTarget"
            layout="${time} [${level}] ${message}" /&gt;
  &lt;/targets&gt;

  &lt;rules&gt;
    &lt;logger name="*" minlevel="Info" writeTo="fileTarget" /&gt;
    &lt;logger name="*" minlevel="Debug" writeTo="consoleTarget" /&gt;
  &lt;/rules&gt;
&lt;/nlog&gt;
            </div>

            <!-- Appendix C: SQL Query Examples -->
            <h3>Appendix C: SQL Query Examples / الملحق ج: أمثلة استعلامات SQL</h3>

            <h4>Basic Queries / الاستعلامات الأساسية</h4>
            <div class="code-block">
-- List all databases / قائمة جميع قواعد البيانات
SELECT datname AS database_name,
       pg_size_pretty(pg_database_size(datname)) AS size
FROM pg_database
WHERE datistemplate = false
ORDER BY datname;

-- List all tables in current database / قائمة جميع الجداول في قاعدة البيانات الحالية
SELECT schemaname, tablename, tableowner
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY schemaname, tablename;

-- Get table column information / الحصول على معلومات أعمدة الجدول
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'your_table_name'
ORDER BY ordinal_position;
            </div>

            <h4>Performance Monitoring / مراقبة الأداء</h4>
            <div class="code-block">
-- Check active connections / فحص الاتصالات النشطة
SELECT pid, usename, application_name, client_addr, state, query_start
FROM pg_stat_activity
WHERE state = 'active'
ORDER BY query_start;

-- Database size information / معلومات حجم قاعدة البيانات
SELECT schemaname,
       tablename,
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Index usage statistics / إحصائيات استخدام الفهارس
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
            </div>

            <h4>Administrative Queries / الاستعلامات الإدارية</h4>
            <div class="code-block">
-- List all users and roles / قائمة جميع المستخدمين والأدوار
SELECT rolname, rolsuper, rolcreaterole, rolcreatedb, rolcanlogin
FROM pg_roles
ORDER BY rolname;

-- Create new user / إنشاء مستخدم جديد
CREATE USER new_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE your_database TO new_user;
GRANT USAGE ON SCHEMA public TO new_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO new_user;

-- Vacuum and analyze tables / تنظيف وتحليل الجداول
VACUUM ANALYZE;

-- Reindex database / إعادة فهرسة قاعدة البيانات
REINDEX DATABASE your_database;
            </div>

            <!-- Appendix D: Error Codes and Messages -->
            <h3>Appendix D: Error Codes and Messages / الملحق د: رموز الأخطاء والرسائل</h3>

            <h4>Common PostgreSQL Error Codes / رموز أخطاء PostgreSQL الشائعة</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Error Code</th>
                            <th>Description / الوصف</th>
                            <th>Solution / الحل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>08001</strong></td>
                            <td>Unable to connect to server / غير قادر على الاتصال بالخادم</td>
                            <td>Check server status and network / تحقق من حالة الخادم والشبكة</td>
                        </tr>
                        <tr>
                            <td><strong>08006</strong></td>
                            <td>Connection failure / فشل الاتصال</td>
                            <td>Verify connection parameters / تحقق من معاملات الاتصال</td>
                        </tr>
                        <tr>
                            <td><strong>28000</strong></td>
                            <td>Invalid authorization / تفويض غير صحيح</td>
                            <td>Check username and password / تحقق من اسم المستخدم وكلمة المرور</td>
                        </tr>
                        <tr>
                            <td><strong>28P01</strong></td>
                            <td>Password authentication failed / فشل مصادقة كلمة المرور</td>
                            <td>Verify credentials / تحقق من بيانات الاعتماد</td>
                        </tr>
                        <tr>
                            <td><strong>3D000</strong></td>
                            <td>Invalid catalog name / اسم كتالوج غير صحيح</td>
                            <td>Check database name / تحقق من اسم قاعدة البيانات</td>
                        </tr>
                        <tr>
                            <td><strong>42601</strong></td>
                            <td>Syntax error / خطأ في بناء الجملة</td>
                            <td>Review SQL syntax / راجع بناء جملة SQL</td>
                        </tr>
                        <tr>
                            <td><strong>42703</strong></td>
                            <td>Undefined column / عمود غير معرف</td>
                            <td>Check column names / تحقق من أسماء الأعمدة</td>
                        </tr>
                        <tr>
                            <td><strong>42P01</strong></td>
                            <td>Undefined table / جدول غير معرف</td>
                            <td>Verify table exists / تحقق من وجود الجدول</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>Application Error Messages / رسائل أخطاء التطبيق</h4>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Error Message</th>
                            <th>Cause / السبب</th>
                            <th>Solution / الحل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>"Service not found"</td>
                            <td>PostgreSQL service not installed / خدمة PostgreSQL غير مثبتة</td>
                            <td>Install PostgreSQL / ثبت PostgreSQL</td>
                        </tr>
                        <tr>
                            <td>"Access denied"</td>
                            <td>Insufficient permissions / أذونات غير كافية</td>
                            <td>Run as administrator / شغل كمسؤول</td>
                        </tr>
                        <tr>
                            <td>"Connection timeout"</td>
                            <td>Network or server issues / مشاكل الشبكة أو الخادم</td>
                            <td>Check connectivity / تحقق من الاتصال</td>
                        </tr>
                        <tr>
                            <td>"Invalid configuration"</td>
                            <td>Configuration file errors / أخطاء ملف التكوين</td>
                            <td>Review settings / راجع الإعدادات</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Conclusion -->
            <div class="success-box">
                <h3>Conclusion / الخاتمة</h3>
                <div class="bilingual">
                    <div class="english">
                        <p>This comprehensive documentation provides complete guidance for using the PostgreSQL Manager application. The application offers professional-grade PostgreSQL management capabilities with an intuitive interface, making database administration accessible to both beginners and experienced users.</p>
                        <p>For additional support or questions, please refer to the troubleshooting section or contact the development team.</p>
                    </div>
                    <div class="arabic">
                        <p>توفر هذه الوثائق الشاملة إرشادات كاملة لاستخدام تطبيق مدير PostgreSQL. يوفر التطبيق إمكانيات إدارة PostgreSQL بمستوى احترافي مع واجهة بديهية، مما يجعل إدارة قواعد البيانات متاحة للمبتدئين والمستخدمين ذوي الخبرة على حد سواء.</p>
                        <p>للحصول على دعم إضافي أو أسئلة، يرجى الرجوع إلى قسم استكشاف الأخطاء وإصلاحها أو الاتصال بفريق التطوير.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>PostgreSQL Manager v1.0</strong><br>
            Complete User Documentation<br>
            Last Updated: June 21, 2025</p>
            <p><strong>مدير PostgreSQL الإصدار 1.0</strong><br>
            دليل المستخدم الشامل<br>
            آخر تحديث: 21 يونيو 2025</p>
        </div>
    </div>
</body>
</html>
