@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: PostgreSQL Manager Portable Startup Script
:: Starts PostgreSQL server and manager application
:: ========================================

echo.
echo ========================================
echo 🚀 Starting PostgreSQL Manager Portable
echo ========================================
echo.

:: Set paths
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%"
set "POSTGRES_BIN=%BASE_DIR%bin"
set "DATA_DIR=%BASE_DIR%data"
set "LOG_DIR=%BASE_DIR%logs"
set "PG_LOG=%LOG_DIR%\postgresql.log"
set "MANAGER_EXE=%BASE_DIR%PostgreSQLManager.exe"

:: Create log directory if it doesn't exist
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: Check prerequisites
echo 🔍 Checking prerequisites...

if not exist "%POSTGRES_BIN%\postgres.exe" (
    echo ❌ Error: PostgreSQL not found
    echo Expected: %POSTGRES_BIN%\postgres.exe
    echo.
    echo Please run 'PostgreSQL_Portable_Setup.bat' first
    pause
    exit /b 1
)

if not exist "%DATA_DIR%\postgresql.conf" (
    echo ❌ Error: Data directory not initialized
    echo Expected: %DATA_DIR%\postgresql.conf
    echo.
    echo Please run 'PostgreSQL_Portable_Setup.bat' first
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

:: Check if PostgreSQL is already running
echo 🔍 Checking if PostgreSQL is already running...
tasklist /FI "IMAGENAME eq postgres.exe" 2>NUL | find /I /N "postgres.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠️ PostgreSQL is already running
    echo Checking if it's our instance...
    
    "%POSTGRES_BIN%\pg_isready.exe" -h localhost -p 5432 -U postgres >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL server is running and accessible
        goto :start_manager
    ) else (
        echo ❌ PostgreSQL is running but not accessible on port 5432
        echo This might be a different PostgreSQL instance
        echo.
        echo Options:
        echo 1. Stop other PostgreSQL instances
        echo 2. Change port in postgresql.conf
        echo 3. Use different port for this instance
        echo.
        pause
        exit /b 1
    )
)

:: Start PostgreSQL server
echo 🚀 Starting PostgreSQL server...
echo Command: "%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" -l "%PG_LOG%" start

"%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" -l "%PG_LOG%" start

if !errorlevel! neq 0 (
    echo ❌ Failed to start PostgreSQL server
    echo.
    echo Troubleshooting steps:
    echo 1. Check log file: %PG_LOG%
    echo 2. Verify data directory: %DATA_DIR%
    echo 3. Check if port 5432 is available
    echo 4. Ensure proper permissions
    echo.
    if exist "%PG_LOG%" (
        echo Last few lines from PostgreSQL log:
        echo ----------------------------------------
        powershell "Get-Content '%PG_LOG%' | Select-Object -Last 10"
        echo ----------------------------------------
    )
    pause
    exit /b 1
)

:: Wait for server to start
echo ⏳ Waiting for PostgreSQL server to start...
set /a "attempts=0"
set /a "max_attempts=30"

:wait_loop
set /a "attempts+=1"
if !attempts! gtr !max_attempts! (
    echo ❌ Timeout waiting for PostgreSQL to start
    echo Check log file: %PG_LOG%
    pause
    exit /b 1
)

"%POSTGRES_BIN%\pg_isready.exe" -h localhost -p 5432 -U postgres >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ PostgreSQL server started successfully
    goto :start_manager
)

echo ⏳ Attempt !attempts!/!max_attempts! - waiting...
timeout /t 2 /nobreak >nul
goto :wait_loop

:start_manager
:: Display server information
echo.
echo 📊 PostgreSQL Server Information:
echo   Status: Running
echo   Host: localhost
echo   Port: 5432
echo   Database: postgres
echo   User: postgres
echo   Data Directory: %DATA_DIR%
echo   Log File: %PG_LOG%

:: Check if PostgreSQL Manager exists
if not exist "%MANAGER_EXE%" (
    echo.
    echo ⚠️ PostgreSQL Manager executable not found
    echo Expected: %MANAGER_EXE%
    echo.
    echo You can still connect to PostgreSQL using:
    echo   Host: localhost
    echo   Port: 5432
    echo   Database: postgres
    echo   Username: postgres
    echo   Password: (none - trust authentication)
    echo.
    echo To connect via command line:
    echo "%POSTGRES_BIN%\psql.exe" -h localhost -p 5432 -U postgres -d postgres
    echo.
    pause
    exit /b 0
)

:: Start PostgreSQL Manager
echo.
echo 🎯 Starting PostgreSQL Manager...
echo Application: %MANAGER_EXE%

:: Check if config file exists and copy it
set "APP_CONFIG=%BASE_DIR%config\PostgreSQLManager.config"
set "TARGET_CONFIG=%BASE_DIR%PostgreSQLManager.exe.config"

if exist "%APP_CONFIG%" (
    echo 📝 Applying portable configuration...
    copy "%APP_CONFIG%" "%TARGET_CONFIG%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Configuration applied successfully
    ) else (
        echo ⚠️ Warning: Could not apply configuration
    )
)

:: Launch PostgreSQL Manager
start "" "%MANAGER_EXE%"

if !errorlevel! equ 0 (
    echo ✅ PostgreSQL Manager started successfully
) else (
    echo ❌ Failed to start PostgreSQL Manager
    echo Check if the executable is valid and dependencies are installed
)

echo.
echo ========================================
echo ✅ PostgreSQL Manager Portable Started!
echo ========================================
echo.
echo 🌐 Connection Information:
echo   Host: localhost
echo   Port: 5432
echo   Database: postgres
echo   Username: postgres
echo   Password: (none required)
echo.
echo 📁 Important Directories:
echo   Data: %DATA_DIR%
echo   Logs: %LOG_DIR%
echo   Backups: %BASE_DIR%backups
echo.
echo 🛠️ Management Commands:
echo   Stop System: Run 'Stop_PostgreSQL_Manager.bat'
echo   View Logs: Check files in '%LOG_DIR%'
echo   Command Line: "%POSTGRES_BIN%\psql.exe" -h localhost -U postgres
echo.
echo 💡 Tips:
echo   - The PostgreSQL Manager should automatically detect the portable setup
echo   - If connection fails, check the Database Manager tab settings
echo   - Use the Test Results tab to verify all components are working
echo.
echo Press any key to close this window...
pause >nul
