<UserControl x:Class="PostgreSQLManager.Views.ServiceControlView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <Style x:Key="ServiceButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
        
        <Style x:Key="StatusCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="PostgreSQL Service Control" 
                   FontSize="24" FontWeight="Bold" 
                   Margin="20,20,20,10" 
                   Foreground="#FF2D2D30"/>

        <!-- Service Status Card -->
        <Border Grid.Row="1" Style="{StaticResource StatusCardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Indicator -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Width="20" Height="20" Margin="0,0,15,0">
                        <Ellipse.Fill>
                            <SolidColorBrush Color="{Binding ServiceStatus.StatusColor}"/>
                        </Ellipse.Fill>
                    </Ellipse>
                    <StackPanel>
                        <TextBlock Text="{Binding ServiceStatus.StatusDescription}" 
                                   FontSize="18" FontWeight="Bold"/>
                        <TextBlock Text="{Binding ServiceStatus.ServiceName}" 
                                   FontSize="12" Foreground="Gray"/>
                    </StackPanel>
                </StackPanel>

                <!-- Service Information -->
                <StackPanel Grid.Column="1" Margin="20,0" VerticalAlignment="Center">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Uptime:" FontWeight="Bold" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ServiceStatus.UptimeString}" Margin="0,0,20,5"/>
                        
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="Memory:" FontWeight="Bold" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding ServiceStatus.MemoryUsageString}" Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="CPU:" FontWeight="Bold" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ServiceStatus.CpuUsageString}" Margin="0,0,20,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="Process ID:" FontWeight="Bold" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding ServiceStatus.ProcessId}" Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Checked:" FontWeight="Bold" Margin="0,0,10,0"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ServiceStatus.LastChecked, StringFormat='yyyy-MM-dd HH:mm:ss'}" Margin="0,0,20,0"/>
                    </Grid>
                </StackPanel>

                <!-- Refresh Button -->
                <Button Grid.Column="2" Content="🔄 Refresh" 
                        Command="{Binding RefreshStatusCommand}"
                        Style="{StaticResource ServiceButtonStyle}"
                        Background="#FF007ACC" Foreground="White"
                        VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- Service Control Buttons -->
        <Border Grid.Row="2" Style="{StaticResource StatusCardStyle}">
            <StackPanel>
                <TextBlock Text="Service Control" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                
                <WrapPanel HorizontalAlignment="Center">
                    <Button Content="▶️ Start Service" 
                            Command="{Binding StartServiceCommand}"
                            IsEnabled="{Binding CanStartService}"
                            Style="{StaticResource ServiceButtonStyle}"
                            Background="#FF28A745" Foreground="White"/>
                    
                    <Button Content="⏹️ Stop Service" 
                            Command="{Binding StopServiceCommand}"
                            IsEnabled="{Binding CanStopService}"
                            Style="{StaticResource ServiceButtonStyle}"
                            Background="#FFDC3545" Foreground="White"/>
                    
                    <Button Content="🔄 Restart Service" 
                            Command="{Binding RestartServiceCommand}"
                            IsEnabled="{Binding CanRestartService}"
                            Style="{StaticResource ServiceButtonStyle}"
                            Background="#FF17A2B8" Foreground="White"/>
                    
                    <Button Content="⏸️ Pause Service" 
                            Command="{Binding PauseServiceCommand}"
                            IsEnabled="{Binding CanPauseService}"
                            Style="{StaticResource ServiceButtonStyle}"
                            Background="#FFFFC107" Foreground="Black"/>
                    
                    <Button Content="▶️ Continue Service" 
                            Command="{Binding ContinueServiceCommand}"
                            IsEnabled="{Binding CanContinueService}"
                            Style="{StaticResource ServiceButtonStyle}"
                            Background="#FF6F42C1" Foreground="White"/>
                </WrapPanel>
            </StackPanel>
        </Border>

        <!-- Operation Progress -->
        <Border Grid.Row="3" Style="{StaticResource StatusCardStyle}" 
                Visibility="{Binding IsOperationInProgress, Converter={StaticResource BoolToVisibilityConverter}}">
            <StackPanel>
                <TextBlock Text="Operation in Progress" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <TextBlock Text="{Binding OperationMessage}" FontSize="14" Margin="0,0,0,10"/>
                <ProgressBar Height="20" Value="{Binding OperationProgress}" Maximum="100" 
                             IsIndeterminate="{Binding IsOperationInProgress}"/>
                <TextBlock Text="{Binding OperationProgress, StringFormat='{}{0:F0}%'}" 
                           HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Service Capabilities Info -->
        <Border Grid.Row="3" Style="{StaticResource StatusCardStyle}"
                Visibility="{Binding IsOperationInProgress, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Inverse}">
            <StackPanel>
                <TextBlock Text="Service Capabilities" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <CheckBox Content="Can Start" IsChecked="{Binding ServiceStatus.CanStart}" IsEnabled="False" Margin="0,5"/>
                        <CheckBox Content="Can Stop" IsChecked="{Binding ServiceStatus.CanStop}" IsEnabled="False" Margin="0,5"/>
                        <CheckBox Content="Can Pause" IsChecked="{Binding ServiceStatus.CanPause}" IsEnabled="False" Margin="0,5"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1">
                        <CheckBox Content="Can Continue" IsChecked="{Binding ServiceStatus.CanContinue}" IsEnabled="False" Margin="0,5"/>
                        <CheckBox Content="Is Operational" IsChecked="{Binding ServiceStatus.IsOperational, Mode=OneWay}" IsEnabled="False" Margin="0,5"/>
                        <CheckBox Content="Is Transitioning" IsChecked="{Binding ServiceStatus.IsTransitioning, Mode=OneWay}" IsEnabled="False" Margin="0,5"/>
                    </StackPanel>
                </Grid>

                <Separator Margin="0,15"/>
                
                <TextBlock Text="Service Details" FontWeight="Bold" Margin="0,0,0,10"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Display Name:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding ServiceStatus.DisplayName}" Margin="0,0,0,5"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Start Type:" FontWeight="Bold" Margin="0,0,10,5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding ServiceStatus.StartType}" Margin="0,0,0,5"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Description:" FontWeight="Bold" Margin="0,0,10,0"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ServiceStatus.Description}" 
                               TextWrapping="Wrap" Margin="0,0,0,0"/>
                </Grid>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
