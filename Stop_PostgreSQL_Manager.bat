@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: PostgreSQL Manager Portable Shutdown Script
:: Gracefully stops PostgreSQL server and manager
:: ========================================

echo.
echo ========================================
echo 🛑 Stopping PostgreSQL Manager Portable
echo ========================================
echo.

:: Set paths
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%"
set "POSTGRES_BIN=%BASE_DIR%bin"
set "DATA_DIR=%BASE_DIR%data"
set "LOG_DIR=%BASE_DIR%logs"

:: Check if PostgreSQL is running
echo 🔍 Checking PostgreSQL status...
tasklist /FI "IMAGENAME eq postgres.exe" 2>NUL | find /I /N "postgres.exe" >NUL
if "%ERRORLEVEL%"=="1" (
    echo ℹ️ PostgreSQL is not running
    goto :stop_manager
)

echo 📊 PostgreSQL is running, checking accessibility...
"%POSTGRES_BIN%\pg_isready.exe" -h localhost -p 5432 -U postgres >nul 2>&1
if !errorlevel! neq 0 (
    echo ⚠️ PostgreSQL is running but not accessible on port 5432
    echo This might be a different PostgreSQL instance
    echo.
    echo Do you want to force stop all postgres.exe processes? (Y/N)
    set /p "choice=Enter choice: "
    if /i "!choice!"=="Y" (
        echo 🔨 Force stopping all PostgreSQL processes...
        taskkill /f /im postgres.exe >nul 2>&1
        echo ✅ All PostgreSQL processes stopped
    ) else (
        echo ℹ️ Skipping PostgreSQL shutdown
    )
    goto :stop_manager
)

:: Graceful shutdown of PostgreSQL
echo 🛑 Gracefully stopping PostgreSQL server...
echo Command: "%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" stop -m fast

"%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" stop -m fast

if !errorlevel! equ 0 (
    echo ✅ PostgreSQL server stopped successfully
) else (
    echo ⚠️ Graceful shutdown failed, trying immediate shutdown...
    "%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" stop -m immediate
    
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL server stopped (immediate mode)
    ) else (
        echo ❌ Failed to stop PostgreSQL server gracefully
        echo Attempting force stop...
        taskkill /f /im postgres.exe >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ PostgreSQL processes terminated
        ) else (
            echo ❌ Could not stop PostgreSQL processes
        )
    )
)

:: Wait a moment for cleanup
timeout /t 2 /nobreak >nul

:: Verify PostgreSQL is stopped
echo 🔍 Verifying PostgreSQL shutdown...
tasklist /FI "IMAGENAME eq postgres.exe" 2>NUL | find /I /N "postgres.exe" >NUL
if "%ERRORLEVEL%"=="1" (
    echo ✅ PostgreSQL completely stopped
) else (
    echo ⚠️ Some PostgreSQL processes may still be running
    echo You may need to manually stop them from Task Manager
)

:stop_manager
:: Stop PostgreSQL Manager if running
echo 🔍 Checking for PostgreSQL Manager processes...
tasklist /FI "IMAGENAME eq PostgreSQLManager.exe" 2>NUL | find /I /N "PostgreSQLManager.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo 🛑 Stopping PostgreSQL Manager...
    taskkill /f /im PostgreSQLManager.exe >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL Manager stopped
    ) else (
        echo ⚠️ Could not stop PostgreSQL Manager automatically
        echo Please close it manually if still running
    )
) else (
    echo ℹ️ PostgreSQL Manager is not running
)

:: Clean up temporary files
echo 🧹 Cleaning up temporary files...
if exist "%BASE_DIR%*.tmp" del "%BASE_DIR%*.tmp" >nul 2>&1
if exist "%DATA_DIR%\postmaster.pid" del "%DATA_DIR%\postmaster.pid" >nul 2>&1

:: Display shutdown summary
echo.
echo ========================================
echo ✅ PostgreSQL Manager Portable Stopped!
echo ========================================
echo.
echo 📊 Shutdown Summary:
echo   PostgreSQL Server: Stopped
echo   PostgreSQL Manager: Stopped
echo   Temporary Files: Cleaned
echo.
echo 📁 Data Preserved:
echo   Database Data: %DATA_DIR%
echo   Log Files: %LOG_DIR%
echo   Backup Files: %BASE_DIR%backups
echo.
echo 🔄 To restart the system:
echo   Run 'Start_PostgreSQL_Manager.bat'
echo.
echo 📋 Maintenance Commands:
echo   View Logs: dir "%LOG_DIR%"
echo   Check Data: dir "%DATA_DIR%"
echo   Manual Start: "%POSTGRES_BIN%\pg_ctl.exe" -D "%DATA_DIR%" start
echo.
echo 💡 Tips:
echo   - All your data is safely preserved
echo   - Logs are available for troubleshooting
echo   - The system can be restarted at any time
echo.
pause
