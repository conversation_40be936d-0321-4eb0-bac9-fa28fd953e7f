@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: PostgreSQL Manager Portable Troubleshooting Script
:: Diagnoses and fixes common portable mode issues
:: ========================================

echo.
echo ========================================
echo 🔧 PostgreSQL Portable Troubleshooting
echo ========================================
echo.

:: Set paths
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%"
set "POSTGRES_BIN=%BASE_DIR%bin"
set "DATA_DIR=%BASE_DIR%data"
set "LOG_DIR=%BASE_DIR%logs"
set "CONFIG_DIR=%BASE_DIR%config"

echo 🔍 Running comprehensive diagnostics...
echo.

:: Check 1: Directory Structure
echo ========================================
echo 📁 DIRECTORY STRUCTURE CHECK
echo ========================================

set "issues_found=0"

echo Checking base directory: %BASE_DIR%
if exist "%BASE_DIR%" (
    echo ✅ Base directory exists
) else (
    echo ❌ Base directory missing: %BASE_DIR%
    set /a "issues_found+=1"
)

echo Checking bin directory: %POSTGRES_BIN%
if exist "%POSTGRES_BIN%" (
    echo ✅ Bin directory exists
    
    if exist "%POSTGRES_BIN%\postgres.exe" (
        echo ✅ postgres.exe found
    ) else (
        echo ❌ postgres.exe missing
        set /a "issues_found+=1"
    )
    
    if exist "%POSTGRES_BIN%\pg_ctl.exe" (
        echo ✅ pg_ctl.exe found
    ) else (
        echo ❌ pg_ctl.exe missing
        set /a "issues_found+=1"
    )
    
    if exist "%POSTGRES_BIN%\psql.exe" (
        echo ✅ psql.exe found
    ) else (
        echo ❌ psql.exe missing
        set /a "issues_found+=1"
    )
    
    if exist "%POSTGRES_BIN%\initdb.exe" (
        echo ✅ initdb.exe found
    ) else (
        echo ❌ initdb.exe missing
        set /a "issues_found+=1"
    )
) else (
    echo ❌ Bin directory missing: %POSTGRES_BIN%
    set /a "issues_found+=1"
)

echo Checking data directory: %DATA_DIR%
if exist "%DATA_DIR%" (
    echo ✅ Data directory exists
    
    if exist "%DATA_DIR%\postgresql.conf" (
        echo ✅ postgresql.conf found
    ) else (
        echo ❌ postgresql.conf missing - data directory not initialized
        set /a "issues_found+=1"
    )
    
    if exist "%DATA_DIR%\pg_hba.conf" (
        echo ✅ pg_hba.conf found
    ) else (
        echo ❌ pg_hba.conf missing
        set /a "issues_found+=1"
    )
) else (
    echo ❌ Data directory missing: %DATA_DIR%
    set /a "issues_found+=1"
)

echo.

:: Check 2: Permissions
echo ========================================
echo 🔒 PERMISSIONS CHECK
echo ========================================

echo Checking write permissions...
echo test > "%BASE_DIR%\test_write.tmp" 2>nul
if exist "%BASE_DIR%\test_write.tmp" (
    echo ✅ Write permissions OK
    del "%BASE_DIR%\test_write.tmp" >nul 2>&1
) else (
    echo ❌ No write permissions to base directory
    set /a "issues_found+=1"
)

echo Checking execute permissions...
"%POSTGRES_BIN%\pg_ctl.exe" --version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Execute permissions OK
) else (
    echo ❌ Cannot execute PostgreSQL binaries
    set /a "issues_found+=1"
)

echo.

:: Check 3: Port Availability
echo ========================================
echo 🌐 PORT AVAILABILITY CHECK
echo ========================================

echo Checking if port 5432 is available...
netstat -an | findstr ":5432" >nul 2>&1
if !errorlevel! equ 0 (
    echo ⚠️ Port 5432 is in use
    echo Checking what's using it:
    netstat -ano | findstr ":5432"
    
    echo.
    echo Checking if it's our PostgreSQL instance...
    "%POSTGRES_BIN%\pg_isready.exe" -h localhost -p 5432 >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Port 5432 is used by accessible PostgreSQL instance
    ) else (
        echo ❌ Port 5432 is blocked by another process
        set /a "issues_found+=1"
    )
) else (
    echo ✅ Port 5432 is available
)

echo.

:: Check 4: Dependencies
echo ========================================
echo 📦 DEPENDENCIES CHECK
echo ========================================

echo Checking Visual C++ Redistributable...
wmic product where "name like '%%Visual C++%%'" get name,version >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Visual C++ Redistributable found
) else (
    echo ⚠️ Visual C++ Redistributable may be missing
    echo This could cause DLL errors
)

echo Checking .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ .NET Framework 4.x found
) else (
    echo ❌ .NET Framework 4.x missing
    set /a "issues_found+=1"
)

echo.

:: Check 5: Process Status
echo ========================================
echo 🔄 PROCESS STATUS CHECK
echo ========================================

echo Checking for running PostgreSQL processes...
tasklist /FI "IMAGENAME eq postgres.exe" 2>NUL | find /I /N "postgres.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ PostgreSQL processes found:
    tasklist /FI "IMAGENAME eq postgres.exe"
) else (
    echo ℹ️ No PostgreSQL processes running
)

echo.
echo Checking for PostgreSQL Manager processes...
tasklist /FI "IMAGENAME eq PostgreSQLManager.exe" 2>NUL | find /I /N "PostgreSQLManager.exe" >NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ PostgreSQL Manager is running
) else (
    echo ℹ️ PostgreSQL Manager is not running
)

echo.

:: Check 6: Log Files
echo ========================================
echo 📋 LOG FILES CHECK
echo ========================================

if exist "%LOG_DIR%" (
    echo ✅ Log directory exists: %LOG_DIR%
    
    echo Recent log files:
    dir "%LOG_DIR%\*.log" /O-D 2>nul | findstr /V "Directory"
    
    echo.
    echo Checking for recent errors in PostgreSQL logs...
    set "latest_log="
    for /f "delims=" %%f in ('dir "%LOG_DIR%\postgresql*.log" /b /o-d 2^>nul') do (
        if not defined latest_log set "latest_log=%%f"
    )
    
    if defined latest_log (
        echo Latest log file: !latest_log!
        echo Last 5 lines:
        echo ----------------------------------------
        powershell "Get-Content '%LOG_DIR%\!latest_log!' | Select-Object -Last 5" 2>nul
        echo ----------------------------------------
    ) else (
        echo ℹ️ No PostgreSQL log files found
    )
) else (
    echo ⚠️ Log directory missing: %LOG_DIR%
)

echo.

:: Summary and Recommendations
echo ========================================
echo 📊 DIAGNOSTIC SUMMARY
echo ========================================

if !issues_found! equ 0 (
    echo ✅ No critical issues found!
    echo Your PostgreSQL portable setup appears to be correctly configured.
    echo.
    echo If you're still experiencing problems:
    echo 1. Try running 'Start_PostgreSQL_Manager.bat'
    echo 2. Check the Test Results tab in PostgreSQL Manager
    echo 3. Review recent log files for specific errors
) else (
    echo ❌ Found !issues_found! issue(s) that need attention
    echo.
    echo RECOMMENDED ACTIONS:
    echo.
    
    if not exist "%POSTGRES_BIN%\postgres.exe" (
        echo 🔧 MISSING POSTGRESQL BINARIES:
        echo    - Ensure PostgreSQL portable is properly extracted
        echo    - Verify the bin directory contains all required executables
        echo    - Re-download PostgreSQL portable if files are missing
        echo.
    )
    
    if not exist "%DATA_DIR%\postgresql.conf" (
        echo 🔧 DATA DIRECTORY NOT INITIALIZED:
        echo    - Run 'PostgreSQL_Portable_Setup.bat' to initialize
        echo    - Or manually run: initdb.exe -D "%DATA_DIR%" -U postgres
        echo.
    )
    
    echo 🔧 GENERAL TROUBLESHOOTING:
    echo    1. Run 'PostgreSQL_Portable_Setup.bat' to reconfigure
    echo    2. Ensure you have write permissions to the directory
    echo    3. Check Windows Defender/Antivirus exclusions
    echo    4. Try running from a different location (e.g., Desktop)
    echo    5. Restart your computer and try again
)

echo.
echo ========================================
echo 🛠️ QUICK FIX OPTIONS
echo ========================================
echo.
echo Choose an action:
echo 1. Run automatic setup/repair
echo 2. Reset data directory (WARNING: Will delete existing data!)
echo 3. Change PostgreSQL port (if port conflict)
echo 4. View detailed system information
echo 5. Exit troubleshooting
echo.
set /p "choice=Enter your choice (1-5): "

if "!choice!"=="1" goto :auto_repair
if "!choice!"=="2" goto :reset_data
if "!choice!"=="3" goto :change_port
if "!choice!"=="4" goto :system_info
if "!choice!"=="5" goto :end

echo Invalid choice. Exiting...
goto :end

:auto_repair
echo.
echo 🔧 Running automatic repair...
call "%BASE_DIR%PostgreSQL_Portable_Setup.bat"
goto :end

:reset_data
echo.
echo ⚠️ WARNING: This will delete all existing database data!
echo Are you sure you want to continue? (Y/N)
set /p "confirm=Enter choice: "
if /i "!confirm!"=="Y" (
    echo Resetting data directory...
    if exist "%DATA_DIR%" rmdir /s /q "%DATA_DIR%"
    call "%BASE_DIR%PostgreSQL_Portable_Setup.bat"
) else (
    echo Reset cancelled.
)
goto :end

:change_port
echo.
echo 🌐 Current port: 5432
set /p "new_port=Enter new port number (e.g., 5433): "
if defined new_port (
    echo Updating postgresql.conf with port !new_port!...
    powershell "(Get-Content '%DATA_DIR%\postgresql.conf') -replace 'port = 5432', 'port = !new_port!' | Set-Content '%DATA_DIR%\postgresql.conf'" 2>nul
    echo ✅ Port updated to !new_port!
    echo Remember to use this port in PostgreSQL Manager connection settings
)
goto :end

:system_info
echo.
echo 💻 SYSTEM INFORMATION:
echo ----------------------------------------
echo Computer: %COMPUTERNAME%
echo User: %USERNAME%
echo OS: 
ver
echo.
echo Memory:
wmic computersystem get TotalPhysicalMemory /value 2>nul | findstr "="
echo.
echo Disk Space:
fsutil volume diskfree %BASE_DIR:~0,2% 2>nul
echo ----------------------------------------
goto :end

:end
echo.
echo 📞 For additional support:
echo    - Review the complete documentation
echo    - Check PostgreSQL official documentation
echo    - Verify system requirements are met
echo.
pause
