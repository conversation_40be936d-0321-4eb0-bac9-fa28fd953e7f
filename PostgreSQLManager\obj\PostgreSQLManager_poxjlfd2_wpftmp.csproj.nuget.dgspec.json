{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\AI Project Final\\AL Tareq Data Base\\PostgreSQLManager\\PostgreSQLManager.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\AI Project Final\\AL Tareq Data Base\\PostgreSQLManager\\PostgreSQLManager.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\AI Project Final\\AL Tareq Data Base\\PostgreSQLManager\\PostgreSQLManager.csproj", "projectName": "PostgreSQLManager", "projectPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\AI Project Final\\AL Tareq Data Base\\PostgreSQLManager\\PostgreSQLManager.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\سطح المكتب\\AI Project Final\\AL Tareq Data Base\\PostgreSQLManager\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"AvalonEdit": {"target": "Package", "version": "[********, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "NLog": {"target": "Package", "version": "[5.2.8, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.3.8, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql": {"target": "Package", "version": "[8.0.3, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}