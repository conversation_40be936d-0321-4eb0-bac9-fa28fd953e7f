# PostgreSQL Manager Portable Configuration Guide

## 🚀 Quick Start Guide

### Prerequisites
- PostgreSQL portable binaries extracted to a folder
- Windows 10/11 or Windows Server 2016+
- .NET Framework 4.8+
- Write permissions to the installation directory

### Setup Process

1. **Extract PostgreSQL Portable**
   ```
   Your_Folder/
   ├── bin/              ← PostgreSQL executables
   ├── lib/              ← Libraries
   ├── share/            ← Shared files
   └── [scripts]         ← Place our scripts here
   ```

2. **Run Initial Setup**
   ```cmd
   PostgreSQL_Portable_Setup.bat
   ```

3. **Start the System**
   ```cmd
   Start_PostgreSQL_Manager.bat
   ```

## 📁 Directory Structure After Setup

```
PostgreSQL_Portable/
├── bin/                          ← PostgreSQL binaries
│   ├── postgres.exe              ← Main server
│   ├── pg_ctl.exe               ← Service control
│   ├── psql.exe                 ← Command line client
│   └── initdb.exe               ← Database initialization
├── data/                         ← Database data (auto-created)
│   ├── postgresql.conf          ← Main configuration
│   ├── pg_hba.conf             ← Authentication rules
│   └── [database files]        ← Actual data
├── logs/                         ← Log files (auto-created)
│   └── postgresql-YYYY-MM-DD.log
├── backups/                      ← Backup storage (auto-created)
├── scripts/                      ← SQL scripts (auto-created)
├── config/                       ← Application config (auto-created)
│   └── PostgreSQLManager.config ← App settings
├── PostgreSQLManager.exe         ← Main application
├── PostgreSQL_Portable_Setup.bat ← Initial setup script
├── Start_PostgreSQL_Manager.bat  ← Startup script
├── Stop_PostgreSQL_Manager.bat   ← Shutdown script
└── Troubleshoot_PostgreSQL_Portable.bat ← Diagnostics
```

## ⚙️ Configuration Details

### PostgreSQL Server Configuration

The setup script automatically configures `postgresql.conf` with optimal portable settings:

```ini
# Connection Settings
listen_addresses = 'localhost'    # Only local connections
port = 5432                       # Standard PostgreSQL port
max_connections = 100             # Reasonable limit for portable

# File Locations (relative paths for portability)
log_directory = '../logs'         # Logs outside data directory
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'

# Memory Settings (optimized for portable use)
shared_buffers = 128MB            # Conservative memory usage
effective_cache_size = 512MB      # Assumes modest system
work_mem = 4MB                    # Per-operation memory
maintenance_work_mem = 64MB       # Maintenance operations

# Performance Settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

### Authentication Configuration

The `pg_hba.conf` is configured for local-only access with trust authentication:

```
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust
```

**Security Note**: Trust authentication is used for simplicity in portable mode. For production use, configure proper authentication methods.

### PostgreSQL Manager Application Settings

The application is configured via `PostgreSQLManager.config`:

```xml
<appSettings>
  <!-- Portable Mode Settings -->
  <add key="IsPortableMode" value="true" />
  <add key="PostgreSQLPath" value="[BASE_DIR]\bin" />
  <add key="DataDirectory" value="[BASE_DIR]\data" />
  <add key="LogDirectory" value="[BASE_DIR]\logs" />
  
  <!-- Connection Settings -->
  <add key="DefaultHost" value="localhost" />
  <add key="DefaultPort" value="5432" />
  <add key="DefaultDatabase" value="postgres" />
  <add key="DefaultUsername" value="postgres" />
  
  <!-- Application Behavior -->
  <add key="StartWithWindows" value="false" />
  <add key="AutoStartService" value="false" />
  <add key="MinimizeToTray" value="true" />
</appSettings>
```

## 🔧 Manual Configuration Steps

### If Automatic Setup Fails

1. **Initialize Data Directory Manually**
   ```cmd
   cd bin
   initdb.exe -D "../data" -U postgres --auth-local=trust --encoding=UTF8
   ```

2. **Start PostgreSQL Manually**
   ```cmd
   cd bin
   pg_ctl.exe -D "../data" -l "../logs/postgresql.log" start
   ```

3. **Test Connection**
   ```cmd
   cd bin
   psql.exe -h localhost -p 5432 -U postgres -d postgres
   ```

### Configuring PostgreSQL Manager Settings

1. **Launch PostgreSQL Manager**
2. **Navigate to Settings Tab**
3. **Configure PostgreSQL Settings:**
   - Installation Path: `[Your_Path]\bin`
   - Data Directory: `[Your_Path]\data`
   - Log Directory: `[Your_Path]\logs`
   - Service Name: (leave empty for portable)

4. **Configure Connection Settings:**
   - Default Host: `localhost`
   - Default Port: `5432`
   - Default Database: `postgres`
   - Default Username: `postgres`

5. **Disable Windows Integration:**
   - Start with Windows: `false`
   - Auto Start Service: `false`

## 🛠️ Troubleshooting Common Issues

### Issue: "postgres.exe not found"
**Solution:**
- Verify PostgreSQL portable is properly extracted
- Check that `bin/postgres.exe` exists
- Ensure no antivirus quarantine

### Issue: "Permission denied"
**Solution:**
```cmd
# Grant permissions to current user
icacls "PostgreSQL_Portable" /grant "%USERNAME%:F" /T
```

### Issue: "Port 5432 already in use"
**Solutions:**
1. **Stop other PostgreSQL instances:**
   ```cmd
   taskkill /f /im postgres.exe
   ```

2. **Use different port:**
   - Edit `data/postgresql.conf`
   - Change `port = 5432` to `port = 5433`
   - Update PostgreSQL Manager connection settings

### Issue: "Data directory not initialized"
**Solution:**
```cmd
cd bin
initdb.exe -D "../data" -U postgres --auth-local=trust --encoding=UTF8
```

### Issue: "Cannot connect to database"
**Checklist:**
1. PostgreSQL server is running
2. Port is correct (5432)
3. Host is localhost
4. Username is postgres
5. No firewall blocking

## 📊 Performance Optimization

### Memory Settings
Adjust based on available system memory:

```ini
# For systems with 4GB RAM
shared_buffers = 128MB
effective_cache_size = 1GB

# For systems with 8GB+ RAM
shared_buffers = 256MB
effective_cache_size = 2GB
```

### Connection Limits
Adjust based on expected usage:

```ini
# Light usage (1-10 concurrent connections)
max_connections = 50

# Moderate usage (10-50 concurrent connections)
max_connections = 100

# Heavy usage (50+ concurrent connections)
max_connections = 200
```

## 🔒 Security Considerations

### For Development Use
- Trust authentication is acceptable
- Local-only connections are secure
- No network exposure

### For Production Use
Consider these security enhancements:

1. **Enable password authentication:**
   ```
   # In pg_hba.conf
   host    all             all             127.0.0.1/32            md5
   ```

2. **Create specific users:**
   ```sql
   CREATE USER myapp WITH PASSWORD 'secure_password';
   GRANT CONNECT ON DATABASE mydb TO myapp;
   ```

3. **Enable SSL:**
   ```ini
   # In postgresql.conf
   ssl = on
   ssl_cert_file = 'server.crt'
   ssl_key_file = 'server.key'
   ```

## 📋 Maintenance Tasks

### Regular Maintenance
```sql
-- Vacuum and analyze (weekly)
VACUUM ANALYZE;

-- Reindex (monthly)
REINDEX DATABASE postgres;

-- Check database size
SELECT pg_size_pretty(pg_database_size('postgres'));
```

### Backup Procedures
```cmd
# Create backup
bin\pg_dump.exe -h localhost -U postgres -d postgres > backups\backup_YYYY-MM-DD.sql

# Restore backup
bin\psql.exe -h localhost -U postgres -d postgres < backups\backup_YYYY-MM-DD.sql
```

### Log Management
- Logs rotate automatically daily
- Keep 30 days of logs by default
- Monitor log size in `logs/` directory

## 🚀 Advanced Configuration

### Custom Port Configuration
1. Edit `data/postgresql.conf`:
   ```ini
   port = 5433
   ```

2. Update startup script:
   ```cmd
   pg_isready.exe -h localhost -p 5433
   ```

3. Update PostgreSQL Manager settings

### Multiple Instances
To run multiple PostgreSQL instances:

1. Use different directories
2. Use different ports
3. Use different data directories
4. Update all configuration files accordingly

## 📞 Support and Resources

### Getting Help
1. Run `Troubleshoot_PostgreSQL_Portable.bat`
2. Check log files in `logs/` directory
3. Review PostgreSQL documentation
4. Check system requirements

### Useful Commands
```cmd
# Check PostgreSQL status
bin\pg_isready.exe -h localhost -p 5432

# Connect to database
bin\psql.exe -h localhost -U postgres

# View server status
bin\pg_ctl.exe -D "data" status

# Stop server
bin\pg_ctl.exe -D "data" stop
```

This configuration guide provides comprehensive instructions for setting up and managing PostgreSQL Manager in portable mode. Follow the scripts and configurations provided for a smooth portable PostgreSQL experience.
