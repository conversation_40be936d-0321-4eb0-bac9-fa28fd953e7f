@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ========================================
:: PostgreSQL Manager Portable Setup Script
:: Automatically configures portable PostgreSQL installation
:: ========================================

echo.
echo ========================================
echo 🚀 PostgreSQL Manager Portable Setup
echo ========================================
echo.

:: Set script directory as base path
set "SCRIPT_DIR=%~dp0"
set "BASE_DIR=%SCRIPT_DIR%"
set "POSTGRES_BIN=%BASE_DIR%bin"
set "DATA_DIR=%BASE_DIR%data"
set "LOG_DIR=%BASE_DIR%logs"
set "BACKUP_DIR=%BASE_DIR%backups"
set "SCRIPTS_DIR=%BASE_DIR%scripts"
set "CONFIG_DIR=%BASE_DIR%config"

:: Create required directories
echo 📁 Creating directory structure...
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
if not exist "%SCRIPTS_DIR%" mkdir "%SCRIPTS_DIR%"
if not exist "%CONFIG_DIR%" mkdir "%CONFIG_DIR%"

:: Check for PostgreSQL executable
echo 🔍 Detecting PostgreSQL installation...
if not exist "%POSTGRES_BIN%\postgres.exe" (
    echo ❌ Error: PostgreSQL not found in bin directory
    echo Expected path: %POSTGRES_BIN%\postgres.exe
    echo.
    echo 📥 Please ensure PostgreSQL portable is extracted to:
    echo %BASE_DIR%
    echo.
    echo Directory structure should be:
    echo %BASE_DIR%bin\postgres.exe
    echo %BASE_DIR%lib\
    echo %BASE_DIR%share\
    echo.
    pause
    exit /b 1
)

echo ✅ PostgreSQL found: %POSTGRES_BIN%\postgres.exe

:: Check if data directory needs initialization
if not exist "%DATA_DIR%\postgresql.conf" (
    echo 🔧 Initializing PostgreSQL data directory...
    echo This may take a few moments...
    
    "%POSTGRES_BIN%\initdb.exe" -D "%DATA_DIR%" -U postgres --auth-local=trust --encoding=UTF8 --locale=C
    
    if !errorlevel! neq 0 (
        echo ❌ Failed to initialize data directory
        echo Check if you have write permissions to: %DATA_DIR%
        pause
        exit /b 1
    )
    
    echo ✅ Data directory initialized successfully
) else (
    echo ✅ Data directory already exists
)

:: Configure postgresql.conf for portable mode
echo ⚙️ Configuring PostgreSQL for portable mode...

set "PG_CONF=%DATA_DIR%\postgresql.conf"
set "TEMP_CONF=%DATA_DIR%\postgresql.conf.tmp"

:: Create optimized postgresql.conf
(
echo # PostgreSQL Portable Configuration
echo # Generated by PostgreSQL Manager Portable Setup
echo.
echo # Connection Settings
echo listen_addresses = 'localhost'
echo port = 5432
echo max_connections = 100
echo.
echo # File Locations
echo log_destination = 'stderr'
echo logging_collector = on
echo log_directory = '../logs'
echo log_filename = 'postgresql-%%Y-%%m-%%d_%%H%%M%%S.log'
echo log_rotation_age = 1d
echo log_rotation_size = 10MB
echo.
echo # Memory Settings ^(optimized for portable^)
echo shared_buffers = 128MB
echo effective_cache_size = 512MB
echo work_mem = 4MB
echo maintenance_work_mem = 64MB
echo.
echo # Performance Settings
echo checkpoint_completion_target = 0.9
echo wal_buffers = 16MB
echo default_statistics_target = 100
echo.
echo # Locale Settings
echo lc_messages = 'C'
echo lc_monetary = 'C'
echo lc_numeric = 'C'
echo lc_time = 'C'
echo default_text_search_config = 'pg_catalog.english'
) > "%TEMP_CONF%"

:: Replace original config
move "%TEMP_CONF%" "%PG_CONF%" >nul 2>&1

:: Configure pg_hba.conf for local access
echo 🔐 Configuring authentication...

set "HBA_CONF=%DATA_DIR%\pg_hba.conf"
set "TEMP_HBA=%DATA_DIR%\pg_hba.conf.tmp"

(
echo # PostgreSQL Portable Authentication Configuration
echo # TYPE  DATABASE        USER            ADDRESS                 METHOD
echo.
echo # Local connections
echo local   all             all                                     trust
echo.
echo # IPv4 local connections
echo host    all             all             127.0.0.1/32            trust
echo.
echo # IPv6 local connections
echo host    all             all             ::1/128                 trust
) > "%TEMP_HBA%"

move "%TEMP_HBA%" "%HBA_CONF%" >nul 2>&1

:: Create PostgreSQL Manager configuration
echo 📝 Creating PostgreSQL Manager configuration...

set "APP_CONFIG=%CONFIG_DIR%\PostgreSQLManager.config"

(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<appSettings^>
echo     ^<!-- PostgreSQL Portable Settings --^>
echo     ^<add key="IsPortableMode" value="true" /^>
echo     ^<add key="PostgreSQLPath" value="%POSTGRES_BIN%" /^>
echo     ^<add key="DataDirectory" value="%DATA_DIR%" /^>
echo     ^<add key="LogDirectory" value="%LOG_DIR%" /^>
echo     ^<add key="BackupDirectory" value="%BACKUP_DIR%" /^>
echo     ^<add key="ScriptsDirectory" value="%SCRIPTS_DIR%" /^>
echo.
echo     ^<!-- Connection Settings --^>
echo     ^<add key="DefaultHost" value="localhost" /^>
echo     ^<add key="DefaultPort" value="5432" /^>
echo     ^<add key="DefaultDatabase" value="postgres" /^>
echo     ^<add key="DefaultUsername" value="postgres" /^>
echo     ^<add key="ConnectionTimeout" value="30" /^>
echo     ^<add key="CommandTimeout" value="30" /^>
echo.
echo     ^<!-- Application Settings --^>
echo     ^<add key="StartWithWindows" value="false" /^>
echo     ^<add key="AutoStartService" value="false" /^>
echo     ^<add key="MinimizeToTray" value="true" /^>
echo     ^<add key="CheckForUpdates" value="false" /^>
echo.
echo     ^<!-- Security Settings --^>
echo     ^<add key="EncryptionEnabled" value="false" /^>
echo     ^<add key="RequireMasterPassword" value="false" /^>
echo     ^<add key="SessionTimeout" value="60" /^>
echo.
echo     ^<!-- UI Settings --^>
echo     ^<add key="Theme" value="Light" /^>
echo     ^<add key="Language" value="en-US" /^>
echo     ^<add key="FontFamily" value="Consolas" /^>
echo     ^<add key="FontSize" value="12" /^>
echo.
echo     ^<!-- Logging Settings --^>
echo     ^<add key="LogLevel" value="Information" /^>
echo     ^<add key="EnableFileLogging" value="true" /^>
echo     ^<add key="EnableConsoleLogging" value="true" /^>
echo     ^<add key="LogRetentionDays" value="30" /^>
echo   ^</appSettings^>
echo ^</configuration^>
) > "%APP_CONFIG%"

:: Set permissions for current user
echo 🔒 Setting up permissions...
icacls "%BASE_DIR%" /grant "%USERNAME%:F" /T >nul 2>&1

echo.
echo ========================================
echo ✅ PostgreSQL Portable Setup Complete!
echo ========================================
echo.
echo 📊 Configuration Summary:
echo   Base Directory: %BASE_DIR%
echo   PostgreSQL Bin: %POSTGRES_BIN%
echo   Data Directory: %DATA_DIR%
echo   Log Directory:  %LOG_DIR%
echo   Port: 5432
echo   Username: postgres
echo   Authentication: Trust (local only)
echo.
echo 🚀 Next Steps:
echo   1. Run 'Start_PostgreSQL_Manager.bat' to start the system
echo   2. Use 'Stop_PostgreSQL_Manager.bat' to stop gracefully
echo   3. Check logs in '%LOG_DIR%' if issues occur
echo.
echo 💡 The PostgreSQL Manager will automatically detect
echo    the portable configuration when started.
echo.
pause
